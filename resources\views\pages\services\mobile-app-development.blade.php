@extends('layouts.app')

@section('title', __('mobile-app-development.page_title'))
@section('meta_description', __('mobile-app-development.meta_description'))
@section('meta_keywords', __('mobile-app-development.meta_keywords'))

@push('structured_data')
@verbatim
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Service",
  "name": "Mobile App Development Services",
  "description": "Leading mobile app development company specializing in cross-platform app development, React Native, Flutter, iOS and Android apps. Expert mobile app development services with competitive pricing.",
  "provider": {
    "@type": "Organization",
    "name": "{{ __('common.company_name') }}",
    "url": "{{ url('/') }}",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ZA",
      "addressRegion": "South Africa"
    }
  },
  "serviceType": "Mobile App Development",
  "areaServed": "Worldwide",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Mobile App Development Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "React Native Development",
          "description": "Cross-platform mobile app development using React Native for iOS and Android"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Flutter App Development",
          "description": "High-performance cross-platform mobile applications using Flutter framework"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Native iOS Development",
          "description": "Native iOS app development using Swift and SwiftUI for iPhone and iPad"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Native Android Development",
          "description": "Native Android app development using Kotlin and Java"
        }
      }
    ]
  },
  "keywords": "mobile app development, React Native, Flutter, iOS development, Android development, cross-platform apps, mobile app development cost, app development South Africa"
}
</script>
@endverbatim
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>

    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="heading-1 text-white mb-6">
                    {{ __('mobile-app-development.hero_title') }}
                </h1>
                <p class="text-lead text-blue-100 mb-8">
                    {{ __('mobile-app-development.hero_description') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                        {{ __('mobile-app-development.get_quote') }}
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="btn-outline bg-white border-white border-2 text-blue-600 font-semibold hover:bg-gray-50 hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                        {{ __('mobile-app-development.view_apps') }}
                    </a>
                </div>
            </div>

            <div class="relative">
                <div class="relative z-10 flex justify-center space-x-4">
                    <!-- iPhone Mockup -->
                    <div class="w-32 h-64 bg-black rounded-3xl p-2 shadow-2xl">
                        <div class="w-full h-full bg-blue-500 rounded-2xl flex flex-col items-center justify-center text-white">
                            <div class="w-8 h-8 bg-white rounded-lg mb-2"></div>
                            <div class="text-xs font-semibold">Your App</div>
                            <div class="text-xs opacity-75">iOS</div>
                        </div>
                    </div>

                    <!-- Android Mockup -->
                    <div class="w-32 h-64 bg-gray-800 rounded-3xl p-2 shadow-2xl">
                        <div class="w-full h-full bg-green-500 rounded-2xl flex flex-col items-center justify-center text-white">
                            <div class="w-8 h-8 bg-white rounded-lg mb-2"></div>
                            <div class="text-xs font-semibold">Your App</div>
                            <div class="text-xs opacity-75">Android</div>
                        </div>
                    </div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute top-10 right-10 w-16 h-16 bg-blue-400 rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute bottom-10 left-10 w-12 h-12 bg-blue-300 rounded-full opacity-30 animate-bounce"></div>
            </div>
        </div>
    </div>

    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- App Types Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                {!! __('mobile-app-development.cross_platform_title') !!}
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                {{ __('mobile-app-development.cross_platform_description') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- React Native Development -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2v8h12V6H4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.react_native_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.react_native_description') }}
                </p>
            </div>

            <!-- Flutter Development -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.flutter_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.flutter_description') }}
                </p>
            </div>

            <!-- Native iOS Development -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-gray-700 to-gray-900 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.native_ios_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.native_ios_description') }}
                </p>
            </div>

            <!-- Educational Apps -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.educational_apps_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.educational_apps_description') }}
                </p>
            </div>

            <!-- Healthcare Apps -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.healthcare_apps_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.healthcare_apps_description') }}
                </p>
            </div>

            <!-- Entertainment Apps -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm3 2h6v4H7V5zm8 8v2h1v-2h-1zm-2-2H7v4h6v-4zm2 0h1V9h-1v2zm1-4V5h-1v2h1zM5 5v2H4V5h1zm0 4H4v2h1V9zm-1 4h1v2H4v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('mobile-app-development.entertainment_apps_title') }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    {{ __('mobile-app-development.entertainment_apps_description') }}
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Development Approaches -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                {!! __('mobile-app-development.technologies_title') !!}
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                {{ __('mobile-app-development.technologies_description') }}
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- React Native -->
            <div class="card-hover text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zM6 4a1 1 0 011-1h6a1 1 0 011 1v10a1 1 0 01-1 1H7a1 1 0 01-1-1V4zm2.5 9a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">React Native Development</h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    {{ __('mobile-app-development.react_native_description') }}
                </p>
                <ul class="text-sm text-gray-500 space-y-2 mb-6">
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Best Performance
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Platform Features
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Native UI/UX
                    </li>
                </ul>
                <div class="text-blue-600 font-semibold">Best for: Complex apps, games, performance-critical applications</div>
            </div>

            <!-- Cross-Platform -->
            <div class="card-hover text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Cross-Platform</h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    {{ __('mobile-app-development.cross_platform_description') }}
                </p>
                <ul class="text-sm text-gray-500 space-y-2 mb-6">
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('mobile-app-development.cost_effective_title') }}
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('mobile-app-development.faster_development_title') }}
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {{ __('mobile-app-development.single_codebase_title') }}
                    </li>
                </ul>
                <div class="text-blue-600 font-semibold">Best for: Business apps, MVPs, budget-conscious projects</div>
            </div>

            <!-- Progressive Web Apps -->
            <div class="card-hover text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">Progressive Web Apps</h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    Web-based applications that work like native apps. No app store required, works across all devices and platforms.
                </p>
                <ul class="text-sm text-gray-500 space-y-2 mb-6">
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        No App Store
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Cross-Platform
                    </li>
                    <li class="flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Easy Updates
                    </li>
                </ul>
                <div class="text-blue-600 font-semibold">Best for: Content apps, simple utilities, quick deployment</div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            {{ __('mobile-app-development.cta_title') }}
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            {{ __('mobile-app-development.cta_description') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                {{ __('mobile-app-development.start_project') }}
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
            <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="btn-outline bg-white border-white border-2 text-blue-600 font-semibold hover:bg-gray-50 hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                {{ __('mobile-app-development.view_apps') }}
            </a>
        </div>
    </div>
</section>

<script type="application/ld+json">
{
    "@type": "Service",
    "name": "Mobile App Development Services",
    "description": "Professional cross-platform mobile app development services using React Native, Flutter, iOS and Android. Expert mobile app development company.",
    "provider": {
        "@type": "Organization",
        "name": "{{ config('app.name') }}",
        "url": "{{ url('/') }}",
        "address": {
            "@type": "PostalAddress",
            "addressCountry": "ZA",
            "addressRegion": "South Africa"
        }
    },
    "serviceType": "Mobile App Development",
    "areaServed": {
        "@type": "Country",
        "name": "South Africa"
    },
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Mobile App Development Services",
        "itemListElement": [
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "React Native Development",
                    "description": "Cross-platform mobile app development using React Native for iOS and Android"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Flutter App Development",
                    "description": "High-performance cross-platform mobile applications using Flutter framework"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Native iOS Development",
                    "description": "Native iOS app development using Swift and SwiftUI for iPhone and iPad"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Native Android Development",
                    "description": "Native Android app development using Kotlin and Java"
                }
            }
        ]
    },
    "keywords": "mobile app development, React Native, Flutter, iOS development, Android development, cross-platform apps, mobile app development cost, app development South Africa"
}
</script>
@endsection