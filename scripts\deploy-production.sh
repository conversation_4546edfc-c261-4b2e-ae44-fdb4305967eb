#!/bin/bash

# Live Chat AI System - Production Deployment Script
# This script automates the deployment process for production environments

set -e  # Exit on any error

# Configuration
APP_DIR="/var/www/chat-system"
BACKUP_DIR="/var/backups/chat-system"
LOG_FILE="/var/log/chat-deployment.log"
MAINTENANCE_SECRET="your-maintenance-secret"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if required commands exist
    local commands=("php" "composer" "npm" "git" "mysql" "redis-cli")
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "Required command '$cmd' not found"
        fi
    done
    
    # Check if application directory exists
    if [[ ! -d "$APP_DIR" ]]; then
        error "Application directory $APP_DIR does not exist"
    fi
    
    # Check if .env file exists
    if [[ ! -f "$APP_DIR/.env" ]]; then
        error "Environment file $APP_DIR/.env does not exist"
    fi
    
    log "Prerequisites check completed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="deployment_backup_$timestamp"
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    info "Backing up database..."
    cd "$APP_DIR"
    php artisan backup:run --only-db --filename="$backup_name.sql" || error "Database backup failed"
    
    # Backup files
    info "Backing up application files..."
    tar -czf "$BACKUP_DIR/${backup_name}_files.tar.gz" \
        -C "$APP_DIR" \
        storage/app/public \
        .env \
        --exclude='storage/app/public/cache' \
        --exclude='storage/logs/*' || error "File backup failed"
    
    log "Backup created successfully: $backup_name"
    echo "$backup_name" > "$BACKUP_DIR/latest_backup.txt"
}

# Enable maintenance mode
enable_maintenance() {
    log "Enabling maintenance mode..."
    cd "$APP_DIR"
    php artisan down --secret="$MAINTENANCE_SECRET" --render="errors::503" || error "Failed to enable maintenance mode"
}

# Disable maintenance mode
disable_maintenance() {
    log "Disabling maintenance mode..."
    cd "$APP_DIR"
    php artisan up || warning "Failed to disable maintenance mode"
}

# Update application code
update_code() {
    log "Updating application code..."
    cd "$APP_DIR"
    
    # Fetch latest changes
    git fetch origin || error "Failed to fetch from origin"
    
    # Get current branch
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    info "Current branch: $current_branch"
    
    # Pull latest changes
    git pull origin "$current_branch" || error "Failed to pull latest changes"
    
    # Get current commit hash
    local commit_hash=$(git rev-parse HEAD)
    info "Deployed commit: $commit_hash"
    
    log "Code update completed"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    cd "$APP_DIR"
    
    # Install PHP dependencies
    info "Installing PHP dependencies..."
    composer install --optimize-autoloader --no-dev --no-interaction || error "Composer install failed"
    
    # Install Node.js dependencies
    info "Installing Node.js dependencies..."
    npm ci --production || error "NPM install failed"
    
    # Build assets
    info "Building assets..."
    npm run production || error "Asset build failed"
    
    log "Dependencies installation completed"
}

# Run database migrations
run_migrations() {
    log "Running database migrations..."
    cd "$APP_DIR"
    
    # Check if there are pending migrations
    if php artisan migrate:status | grep -q "Pending"; then
        info "Pending migrations found, running migrations..."
        php artisan migrate --force || error "Database migration failed"
    else
        info "No pending migrations"
    fi
    
    log "Database migrations completed"
}

# Clear and optimize caches
optimize_application() {
    log "Optimizing application..."
    cd "$APP_DIR"
    
    # Clear all caches
    info "Clearing caches..."
    php artisan optimize:clear || warning "Failed to clear some caches"
    
    # Optimize for production
    info "Optimizing for production..."
    php artisan config:cache || error "Config cache failed"
    php artisan route:cache || error "Route cache failed"
    php artisan view:cache || error "View cache failed"
    php artisan event:cache || error "Event cache failed"
    
    log "Application optimization completed"
}

# Restart services
restart_services() {
    log "Restarting services..."
    
    # Restart queue workers
    info "Restarting queue workers..."
    sudo supervisorctl restart chat-worker:* || warning "Failed to restart queue workers"
    
    # Restart PHP-FPM
    info "Restarting PHP-FPM..."
    sudo systemctl reload php8.1-fpm || warning "Failed to restart PHP-FPM"
    
    # Restart Nginx
    info "Restarting Nginx..."
    sudo systemctl reload nginx || warning "Failed to restart Nginx"
    
    # Restart Echo Server (if running)
    if pgrep -f "laravel-echo-server" > /dev/null; then
        info "Restarting Echo Server..."
        pkill -f "laravel-echo-server"
        sleep 2
        nohup laravel-echo-server start > /dev/null 2>&1 &
    fi
    
    log "Services restart completed"
}

# Run health checks
run_health_checks() {
    log "Running health checks..."
    cd "$APP_DIR"
    
    # Check application status
    info "Checking application status..."
    if ! php artisan about > /dev/null 2>&1; then
        error "Application health check failed"
    fi
    
    # Check database connection
    info "Checking database connection..."
    if ! php artisan tinker --execute="DB::connection()->getPdo();" > /dev/null 2>&1; then
        error "Database connection check failed"
    fi
    
    # Check Redis connection
    info "Checking Redis connection..."
    if ! redis-cli ping > /dev/null 2>&1; then
        error "Redis connection check failed"
    fi
    
    # Check queue workers
    info "Checking queue workers..."
    if ! sudo supervisorctl status chat-worker:* | grep -q "RUNNING"; then
        warning "Some queue workers are not running"
    fi
    
    # Check web server response
    info "Checking web server response..."
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost")
    if [[ "$response_code" != "200" ]]; then
        error "Web server health check failed (HTTP $response_code)"
    fi
    
    log "Health checks completed successfully"
}

# Send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    # Send Slack notification (if configured)
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Deployment $status: $message\"}" \
            "$SLACK_WEBHOOK_URL" > /dev/null 2>&1 || true
    fi
    
    # Send email notification (if configured)
    if [[ -n "${NOTIFICATION_EMAIL:-}" ]]; then
        echo "$message" | mail -s "Deployment $status" "$NOTIFICATION_EMAIL" || true
    fi
}

# Rollback function
rollback() {
    error "Deployment failed, initiating rollback..."
    
    # Disable maintenance mode first
    disable_maintenance
    
    # Get latest backup name
    if [[ -f "$BACKUP_DIR/latest_backup.txt" ]]; then
        local backup_name=$(cat "$BACKUP_DIR/latest_backup.txt")
        warning "Rolling back to backup: $backup_name"
        
        # Restore database
        cd "$APP_DIR"
        php artisan backup:restore --filename="$backup_name.sql" || error "Database rollback failed"
        
        # Restore files (if needed)
        # tar -xzf "$BACKUP_DIR/${backup_name}_files.tar.gz" -C "$APP_DIR"
        
        # Clear caches
        php artisan optimize:clear
        
        # Restart services
        restart_services
        
        send_notification "FAILED" "Deployment failed and rolled back to $backup_name"
    else
        error "No backup found for rollback"
    fi
}

# Main deployment function
deploy() {
    local start_time=$(date +%s)
    
    log "Starting deployment process..."
    
    # Set trap for cleanup on error
    trap rollback ERR
    
    # Run deployment steps
    check_root
    check_prerequisites
    create_backup
    enable_maintenance
    update_code
    install_dependencies
    run_migrations
    optimize_application
    restart_services
    disable_maintenance
    run_health_checks
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log "Deployment completed successfully in ${duration} seconds"
    send_notification "SUCCESS" "Deployment completed successfully in ${duration} seconds"
    
    # Remove trap
    trap - ERR
}

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "rollback")
        rollback
        ;;
    "health-check")
        run_health_checks
        ;;
    "backup")
        create_backup
        ;;
    "maintenance-on")
        enable_maintenance
        ;;
    "maintenance-off")
        disable_maintenance
        ;;
    *)
        echo "Usage: $0 {deploy|rollback|health-check|backup|maintenance-on|maintenance-off}"
        exit 1
        ;;
esac
