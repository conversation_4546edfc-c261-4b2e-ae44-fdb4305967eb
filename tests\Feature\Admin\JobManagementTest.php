<?php

namespace Tests\Feature\Admin;

use App\Models\Job;
use App\Models\JobApplication;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class JobManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin role using firstOrCreate to avoid conflicts
        $adminRole = Role::firstOrCreate(
            ['name' => 'admin'],
            [
                'slug' => 'admin',
                'description' => 'Administrator role with full permissions',
                'permissions' => [
                    'users' => ['create', 'read', 'update', 'delete'],
                    'roles' => ['create', 'read', 'update', 'delete'],
                    'projects' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'services' => ['create', 'read', 'update', 'delete'],
                    'activity_logs' => ['read', 'delete'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );

        $this->admin = User::factory()->create([
            'role_id' => $adminRole->id,
        ]);
    }

    /**
     * Test admin can view jobs index.
     */
    public function test_admin_can_view_jobs_index(): void
    {
        Job::create([
            'title' => 'Software Developer',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $response = $this->actingAs($this->admin)->get('/admin/jobs');

        $response->assertStatus(200);
        $response->assertSee('Job Management');
        $response->assertSee('Software Developer');
        $response->assertSee('Cape Town');
    }

    /**
     * Test admin can create new job.
     */
    public function test_admin_can_create_new_job(): void
    {
        $response = $this->actingAs($this->admin)->get('/admin/jobs/create');
        $response->assertStatus(200);
        $response->assertSee('Post New Job');

        $jobData = [
            'title' => 'New Test Job',
            'description' => 'This is a test job description.',
            'requirements' => 'Bachelor degree required',
            'responsibilities' => 'Manage projects and teams',
            'location' => 'Johannesburg',
            'employment_type' => 'full-time',
            'experience_level' => 'senior',
            'department' => 'Management',
            'salary_period' => 'monthly',
            'salary_min' => 40000,
            'salary_max' => 60000,
            'is_active' => true,
        ];

        $response = $this->actingAs($this->admin)->post('/admin/jobs', $jobData);

        $this->assertDatabaseHas('career_jobs', [
            'title' => 'New Test Job',
            'location' => 'Johannesburg',
            'employment_type' => 'full-time',
        ]);

        $job = Job::where('title', 'New Test Job')->first();
        $response->assertRedirect("/admin/jobs/{$job->slug}");
    }

    /**
     * Test admin can edit existing job.
     */
    public function test_admin_can_edit_existing_job(): void
    {
        $job = Job::create([
            'title' => 'Original Title',
            'description' => 'Original description',
            'requirements' => 'Original requirements',
            'responsibilities' => 'Original responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/jobs/{$job->slug}/edit");
        $response->assertStatus(200);
        $response->assertSee('Edit Job: Original Title');

        $updatedData = [
            'title' => 'Updated Title',
            'description' => 'Updated description',
            'requirements' => 'Updated requirements',
            'responsibilities' => 'Updated responsibilities',
            'location' => 'Durban',
            'employment_type' => 'part-time',
            'experience_level' => 'senior',
            'department' => 'Marketing',
            'salary_period' => 'hourly',
        ];

        $response = $this->actingAs($this->admin)->put("/admin/jobs/{$job->slug}", $updatedData);

        $this->assertDatabaseHas('career_jobs', [
            'id' => $job->id,
            'title' => 'Updated Title',
            'location' => 'Durban',
            'employment_type' => 'part-time',
        ]);

        // Refresh the job to get the updated slug
        $job->refresh();
        $response->assertRedirect("/admin/jobs/{$job->slug}");
    }

    /**
     * Test admin can view job details.
     */
    public function test_admin_can_view_job_details(): void
    {
        $job = Job::create([
            'title' => 'Test Job Details',
            'description' => 'Detailed job description',
            'requirements' => 'Specific requirements',
            'responsibilities' => 'Key responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'salary_min' => 30000,
            'salary_max' => 45000,
        ]);

        // Create some applications for this job
        JobApplication::create([
            'job_id' => $job->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '123456789',
            'country' => 'South Africa',
            'cover_letter' => 'Test cover letter',
            'experience_summary' => 'Test experience',
            'highest_qualification' => 'Bachelor Degree',
        ]);

        $response = $this->actingAs($this->admin)->get("/admin/jobs/{$job->slug}");

        $response->assertStatus(200);
        $response->assertSee('Test Job Details');
        $response->assertSee('Detailed job description');
        $response->assertSee('ZAR 30,000 - 45,000');
        $response->assertSee('1'); // Application count
    }

    /**
     * Test admin can delete job.
     */
    public function test_admin_can_delete_job(): void
    {
        $job = Job::create([
            'title' => 'Job to Delete',
            'description' => 'This job will be deleted',
            'requirements' => 'Requirements',
            'responsibilities' => 'Responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
        ]);

        $response = $this->actingAs($this->admin)->delete("/admin/jobs/{$job->slug}");

        $this->assertDatabaseHas('career_jobs', [
            'id' => $job->id,
            'is_deleted' => true,
        ]);

        $response->assertRedirect('/admin/jobs');
    }

    /**
     * Test admin can toggle job active status.
     */
    public function test_admin_can_toggle_job_active_status(): void
    {
        $job = Job::create([
            'title' => 'Toggle Status Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->admin)->post("/admin/jobs/{$job->slug}/toggle-active");

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $job->refresh();
        $this->assertFalse($job->is_active);
    }

    /**
     * Test admin can toggle job featured status.
     */
    public function test_admin_can_toggle_job_featured_status(): void
    {
        $job = Job::create([
            'title' => 'Toggle Featured Job',
            'description' => 'Test description',
            'requirements' => 'Test requirements',
            'responsibilities' => 'Test responsibilities',
            'location' => 'Cape Town',
            'employment_type' => 'full-time',
            'experience_level' => 'mid',
            'department' => 'Engineering',
            'salary_period' => 'monthly',
            'is_featured' => false,
        ]);

        $response = $this->actingAs($this->admin)->post("/admin/jobs/{$job->slug}/toggle-featured");

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $job->refresh();
        $this->assertTrue($job->is_featured);
    }

    /**
     * Test job creation validation.
     */
    public function test_job_creation_validation(): void
    {
        $response = $this->actingAs($this->admin)->post('/admin/jobs', []);

        $response->assertSessionHasErrors([
            'title',
            'description',
            'requirements',
            'responsibilities',
            'location',
            'employment_type',
            'experience_level',
            'department',
            'salary_period',
        ]);
    }

    /**
     * Test non-admin cannot access job management.
     */
    public function test_non_admin_cannot_access_job_management(): void
    {
        $customerRole = Role::firstOrCreate(
            ['name' => 'customer'],
            [
                'slug' => 'customer',
                'description' => 'Customer role with basic permissions',
                'permissions' => [
                    'profile' => ['read', 'update'],
                    'projects' => ['create', 'read'],
                    'orders' => ['create', 'read'],
                    'dashboard' => ['access'],
                ],
                'is_active' => true,
            ]
        );
        $user = User::factory()->create(['role_id' => $customerRole->id]);

        $response = $this->actingAs($user)->get('/admin/jobs');
        $response->assertStatus(403);

        $response = $this->actingAs($user)->get('/admin/jobs/create');
        $response->assertStatus(403);
    }

    /**
     * Test guest cannot access job management.
     */
    public function test_guest_cannot_access_job_management(): void
    {
        $response = $this->get('/admin/jobs');
        $response->assertRedirect('/login');

        $response = $this->get('/admin/jobs/create');
        $response->assertRedirect('/login');
    }
}
