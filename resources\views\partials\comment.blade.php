@php
    $maxLevel = 3; // Maximum nesting level for replies
    $indentClass = $level > 0 ? 'ml-' . min($level * 8, 24) : '';
@endphp

<div class="comment-item {{ $indentClass }}" data-comment-id="{{ $comment->uuid }}">
    <div class="flex space-x-4 p-4 {{ $level > 0 ? 'bg-gray-50' : 'bg-white' }} rounded-lg {{ $level > 0 ? 'border-l-2 border-blue-200' : '' }}">
        <!-- User Avatar -->
        <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                <span class="text-white font-semibold text-sm">
                    {{ substr($comment->user->first_name, 0, 1) }}{{ substr($comment->user->last_name, 0, 1) }}
                </span>
            </div>
        </div>

        <!-- Comment Content -->
        <div class="flex-1 min-w-0">
            <!-- Comment Header -->
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                    <h4 class="text-sm font-semibold text-gray-900">
                        {{ $comment->user->first_name }} {{ $comment->user->last_name }}
                    </h4>
                    <span class="text-xs text-gray-500">{{ $comment->time_ago }}</span>
                    
                    <!-- Rating Display -->
                    @if($comment->rating)
                        <div class="flex items-center space-x-1">
                            <div class="text-yellow-400 text-sm">{{ $comment->star_rating }}</div>
                            <span class="text-xs text-gray-500">({{ $comment->rating }}/5)</span>
                        </div>
                    @endif
                </div>

                <!-- Comment Actions -->
                <div class="flex items-center space-x-2">
                    @auth
                        <!-- Helpful Button -->
                        <button type="button" 
                                class="helpful-btn text-xs text-gray-500 hover:text-blue-600 transition-colors flex items-center space-x-1"
                                data-comment-id="{{ $comment->uuid }}"
                                data-is-helpful="{{ $comment->isMarkedHelpfulBy(Auth::id()) ? 'true' : 'false' }}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-1.5-1.5M9 7v10"></path>
                            </svg>
                            <span class="helpful-count">{{ $comment->helpful_count }}</span>
                        </button>

                        <!-- Reply Button -->
                        @if($level < $maxLevel)
                            <button type="button" 
                                    class="reply-btn text-xs text-gray-500 hover:text-blue-600 transition-colors"
                                    data-comment-id="{{ $comment->uuid }}"
                                    data-user-name="{{ $comment->user->first_name }} {{ $comment->user->last_name }}">
                                Reply
                            </button>
                        @endif

                        <!-- Flag Button -->
                        <button type="button" 
                                class="flag-btn text-xs text-gray-500 hover:text-red-600 transition-colors"
                                data-comment-id="{{ $comment->uuid }}">
                            Flag
                        </button>
                    @endauth
                </div>
            </div>

            <!-- Comment Text -->
            <div class="prose prose-sm max-w-none mb-3">
                {!! nl2br(e($comment->content)) !!}
            </div>

            <!-- Attachments -->
            @if($comment->has_attachments)
                <div class="mb-3">
                    <h5 class="text-sm font-medium text-gray-700 mb-2">Attachments ({{ $comment->attachment_count }})</h5>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        @foreach($comment->getOptimizedAttachments() as $attachment)
                            <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors">
                                <div class="flex items-center space-x-3">
                                    <!-- File Icon -->
                                    <div class="flex-shrink-0">
                                        @if($attachment['type'] === 'image')
                                            <img src="{{ $attachment['optimized'] }}" 
                                                 alt="Attachment" 
                                                 class="w-12 h-12 object-cover rounded cursor-pointer"
                                                 onclick="openImageModal('{{ $attachment['optimized'] }}')">
                                        @else
                                            <div class="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                                                @switch($attachment['type'])
                                                    @case('document')
                                                        <svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        @break
                                                    @case('word')
                                                        <svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        @break
                                                    @case('excel')
                                                        <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                                        </svg>
                                                        @break
                                                    @default
                                                        <svg class="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                                        </svg>
                                                @endswitch
                                            </div>
                                        @endif
                                    </div>

                                    <!-- File Info -->
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ basename($attachment['original']) }}
                                        </p>
                                        @if($attachment['size'])
                                            <p class="text-xs text-gray-500">
                                                {{ number_format($attachment['size'] / 1024, 1) }} KB
                                            </p>
                                        @endif
                                    </div>

                                    <!-- Download Button -->
                                    <a href="{{ route('comments.download', ['comment' => $comment->uuid, 'filename' => $attachment['original']]) }}" 
                                       class="text-blue-600 hover:text-blue-800 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Replies -->
            @if($comment->replies->count() > 0)
                <div class="mt-4 space-y-4">
                    @foreach($comment->replies as $reply)
                        @include('partials.comment', ['comment' => $reply, 'level' => $level + 1])
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Image Modal for Attachments -->
<div id="image-modal" class="fixed inset-0 bg-black bg-opacity-75 z-50 hidden flex items-center justify-center" onclick="closeImageModal()">
    <div class="relative max-w-4xl max-h-full p-4">
        <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 z-10">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
        <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain">
    </div>
</div>
