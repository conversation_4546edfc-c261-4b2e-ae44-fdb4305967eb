@extends('layouts.dashboard')

@section('title', __('common.dashboard') . ' - ' . __('common.company_name'))
@section('page_title', __('common.dashboard'))

@section('content')
<div class="p-6 space-y-8">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-br from-primary-800 via-primary-700 to-secondary-700 rounded-xl p-6 md:p-8 text-white shadow-strong">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div class="space-y-3">
                <h1 class="text-2xl md:text-3xl font-bold">
                    Welcome back, {{ auth()->user()->first_name }}! 👋
                </h1>
                <p class="text-blue-100 text-base md:text-lg">
                    @if($user_type === 'customer')
                        Manage your orders and explore our products
                    @else
                        Track your projects and manage your account
                    @endif
                </p>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-secondary-400 rounded-full animate-pulse"></div>
                    <span class="text-sm text-blue-200">Account Active</span>
                </div>
            </div>
            <div class="flex-shrink-0">
                <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-4 md:p-6 border border-white border-opacity-20">
                    <div class="text-center space-y-2">
                        <div class="w-10 h-10 md:w-12 md:h-12 bg-secondary-600 rounded-full flex items-center justify-center mx-auto mb-2 md:mb-3">
                            <svg class="w-5 h-5 md:w-6 md:h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                            </svg>
                        </div>
                        <div class="text-xl md:text-2xl font-bold">{{ ucfirst($user_type) }}</div>
                        <div class="text-xs md:text-sm text-blue-200">Account Type</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        @if($user_type === 'customer')
            <!-- Total Orders -->
            <div class="bg-white rounded-xl shadow-soft p-4 md:p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex items-center space-x-3 md:space-x-4">
                        <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300">
                            <svg class="w-5 h-5 md:w-6 md:h-6 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-neutral-600">Total Orders</p>
                            <p class="text-xl md:text-2xl font-bold text-primary-900">{{ $quick_stats['total_orders'] }}</p>
                        </div>
                    </div>
                    <div class="text-left sm:text-right">
                        <span class="inline-flex items-center px-2 md:px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                            All time
                        </span>
                    </div>
                </div>
            </div>

            <!-- Total Spent -->
            <div class="bg-white rounded-xl shadow-soft p-4 md:p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex items-center space-x-3 md:space-x-4">
                        <div class="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-xl flex items-center justify-center group-hover:from-secondary-200 group-hover:to-secondary-300 transition-all duration-300">
                            <svg class="w-5 h-5 md:w-6 md:h-6 text-secondary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-xs md:text-sm font-medium text-neutral-600">Total Spent</p>
                            <p class="text-xl md:text-2xl font-bold text-primary-900">R{{ number_format($quick_stats['total_spent'], 2) }}</p>
                        </div>
                    </div>
                    <div class="text-left sm:text-right">
                        <span class="inline-flex items-center px-2 md:px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800">
                            Lifetime
                        </span>
                    </div>
                </div>
            </div>

            <!-- Pending Orders -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center group-hover:bg-warning-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Pending Orders</p>
                            <p class="text-2xl font-bold text-primary-900">{{ $quick_stats['pending_orders'] }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800">
                            Active
                        </span>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center group-hover:bg-primary-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Account Status</p>
                            <p class="text-2xl font-bold text-primary-900">{{ ucfirst($quick_stats['account_status']) }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                            <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1"></div>
                            Verified
                        </span>
                    </div>
                </div>
            </div>
        @else
            <!-- Total Projects -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Total Projects</p>
                            <p class="text-2xl font-bold text-primary-900">{{ $quick_stats['total_projects'] }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-secondary-100 text-secondary-800">
                            All time
                        </span>
                    </div>
                </div>
            </div>

            <!-- Active Projects -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-success-100 rounded-xl flex items-center justify-center group-hover:bg-success-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Active Projects</p>
                            <p class="text-2xl font-bold text-primary-900">{{ $quick_stats['active_projects'] }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                            <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1 animate-pulse-subtle"></div>
                            In Progress
                        </span>
                    </div>
                </div>
            </div>

            <!-- Project Value -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center group-hover:bg-accent-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Project Value</p>
                            <p class="text-2xl font-bold text-primary-900">R{{ number_format($quick_stats['total_project_value'], 2) }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-accent-100 text-accent-800">
                            Total
                        </span>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div class="bg-white rounded-xl shadow-soft p-6 border border-neutral-100 hover:shadow-medium transition-all duration-300 group">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center group-hover:bg-primary-200 transition-colors duration-300">
                            <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-primary-600">Account Status</p>
                            <p class="text-2xl font-bold text-primary-900">{{ ucfirst($quick_stats['account_status']) }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                            <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1"></div>
                            Verified
                        </span>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Recent Activity -->
        <div class="lg:col-span-2">
            @if($user_type === 'customer')
                <!-- Recent Orders -->
                <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                    <div class="px-6 py-5 border-b border-neutral-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-primary-900">Recent Orders</h3>
                            </div>
                            <a href="{{ route('orders.index') }}" class="inline-flex items-center text-sm font-medium text-accent-600 hover:text-accent-700 transition-colors duration-200">
                                View all
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        @if($recent_orders->count() > 0)
                            <div class="space-y-4">
                                @foreach($recent_orders as $order)
                                    <div class="flex items-center justify-between p-4 border border-neutral-200 rounded-xl hover:bg-neutral-50 hover:border-accent-200 transition-all duration-200 group">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-12 h-12 bg-accent-100 rounded-xl flex items-center justify-center group-hover:bg-accent-200 transition-colors duration-200">
                                                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-semibold text-primary-900">Order #{{ $order->order_number }}</h4>
                                                <p class="text-sm text-primary-600">{{ $order->created_at->format('M j, Y') }} • {{ $order->items->count() }} items</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="text-right">
                                                <p class="text-sm font-semibold text-primary-900">R{{ number_format($order->total_amount, 2) }}</p>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                    @if($order->status === 'completed') bg-success-100 text-success-800
                                                    @elseif($order->status === 'pending') bg-warning-100 text-warning-800
                                                    @elseif($order->status === 'processing') bg-accent-100 text-accent-800
                                                    @elseif($order->status === 'shipped') bg-secondary-100 text-secondary-800
                                                    @else bg-neutral-100 text-neutral-800
                                                    @endif">
                                                    @if($order->status === 'completed')
                                                        <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1.5"></div>
                                                    @elseif($order->status === 'pending')
                                                        <div class="w-1.5 h-1.5 bg-warning-500 rounded-full mr-1.5 animate-pulse-subtle"></div>
                                                    @elseif($order->status === 'processing')
                                                        <div class="w-1.5 h-1.5 bg-accent-500 rounded-full mr-1.5 animate-pulse-subtle"></div>
                                                    @elseif($order->status === 'shipped')
                                                        <div class="w-1.5 h-1.5 bg-secondary-500 rounded-full mr-1.5"></div>
                                                    @endif
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                            </div>
                                            <a href="{{ route('orders.show', $order->id) }}" class="p-2 text-primary-600 hover:text-accent-600 hover:bg-accent-50 rounded-lg transition-all duration-200">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-primary-900 mb-2">No orders yet</h3>
                                <p class="text-primary-600 mb-6">Start shopping to see your orders here.</p>
                                <a href="{{ route('shop.index') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-accent-600 hover:bg-accent-700 transition-colors duration-200 shadow-soft">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                    Browse Products
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <!-- Recent Projects -->
                <div class="bg-white rounded-xl shadow-soft border border-neutral-100 mb-6">
                    <div class="px-6 py-5 border-b border-neutral-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-primary-900">Recent Projects</h3>
                            </div>
                            <a href="{{ route('projects.index') }}" class="inline-flex items-center text-sm font-medium text-accent-600 hover:text-accent-700 transition-colors duration-200">
                                View all
                                <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="p-6">
                        @if($recent_projects->count() > 0)
                            <div class="space-y-4">
                                @foreach($recent_projects as $project)
                                    <div class="flex items-center justify-between p-4 border border-neutral-200 rounded-xl hover:bg-neutral-50 hover:border-secondary-200 transition-all duration-200 group">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="w-12 h-12 bg-secondary-100 rounded-xl flex items-center justify-center group-hover:bg-secondary-200 transition-colors duration-200">
                                                    <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-semibold text-primary-900">{{ $project->title }}</h4>
                                                <p class="text-sm text-primary-600">Updated {{ $project->updated_at ? $project->updated_at->diffForHumans() : 'recently' }}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                                @if($project->status === 'completed') bg-success-100 text-success-800
                                                @elseif($project->status === 'active' || $project->status === 'in_progress') bg-accent-100 text-accent-800
                                                @elseif($project->status === 'on_hold') bg-warning-100 text-warning-800
                                                @else bg-neutral-100 text-neutral-800
                                                @endif">
                                                @if($project->status === 'completed')
                                                    <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1.5"></div>
                                                @elseif($project->status === 'active' || $project->status === 'in_progress')
                                                    <div class="w-1.5 h-1.5 bg-accent-500 rounded-full mr-1.5 animate-pulse-subtle"></div>
                                                @elseif($project->status === 'on_hold')
                                                    <div class="w-1.5 h-1.5 bg-warning-500 rounded-full mr-1.5"></div>
                                                @endif
                                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                            </span>
                                            @if($project->slug)
                                                <a href="{{ route('projects.show', $project->slug) }}" class="p-2 text-primary-600 hover:text-secondary-600 hover:bg-secondary-50 rounded-lg transition-all duration-200">
                                            @else
                                                <span class="p-2 text-gray-400 rounded-lg">
                                            @endif
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                                </svg>
                                            @if($project->slug)
                                                </a>
                                            @else
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <h3 class="text-lg font-semibold text-primary-900 mb-2">No projects yet</h3>
                                <p class="text-primary-600 mb-6">Contact us to start your first project.</p>
                                <a href="{{ route('contact') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-secondary-600 hover:bg-secondary-700 transition-colors duration-200 shadow-soft">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                    Get Started
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Recent Orders (for clients who also shop) -->
                @if($recent_orders->count() > 0)
                    <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                        <div class="px-6 py-5 border-b border-neutral-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-semibold text-primary-900">Recent Orders</h3>
                                </div>
                                <a href="{{ route('orders.index') }}" class="inline-flex items-center text-sm font-medium text-accent-600 hover:text-accent-700 transition-colors duration-200">
                                    View all
                                    <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-3">
                                @foreach($recent_orders->take(3) as $order)
                                    <div class="flex items-center justify-between p-3 border border-neutral-200 rounded-lg hover:bg-neutral-50 hover:border-accent-200 transition-all duration-200">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h4 class="text-sm font-medium text-primary-900">#{{ $order->order_number }}</h4>
                                                <p class="text-xs text-primary-600">{{ $order->created_at->format('M j') }}</p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-primary-900">R{{ number_format($order->total_amount, 0) }}</p>
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                                @if($order->status === 'completed') bg-success-100 text-success-700
                                                @elseif($order->status === 'pending') bg-warning-100 text-warning-700
                                                @elseif($order->status === 'processing') bg-accent-100 text-accent-700
                                                @else bg-neutral-100 text-neutral-700
                                                @endif">
                                                {{ ucfirst($order->status) }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-5 border-b border-neutral-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-primary-900">Quick Actions</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-2 md:space-y-3">
                        @if($user_type === 'customer')
                            <a href="{{ route('shop.index') }}" class="flex items-center p-3 md:p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 hover:text-primary-800 transition-all duration-200 group border border-transparent hover:border-primary-200 hover:shadow-sm">
                                <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center mr-3 md:mr-4 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-200">
                                    <svg class="w-4 h-4 md:w-5 md:h-5 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm md:text-base">Browse Products</p>
                                    <p class="text-xs text-primary-600">Explore our catalog</p>
                                </div>
                            </a>
                            <a href="{{ route('cart.index') }}" class="flex items-center p-3 md:p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-gradient-to-r hover:from-secondary-50 hover:to-secondary-100 hover:text-secondary-800 transition-all duration-200 group border border-transparent hover:border-secondary-200 hover:shadow-sm">
                                <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-lg flex items-center justify-center mr-3 md:mr-4 group-hover:from-secondary-200 group-hover:to-secondary-300 transition-all duration-200">
                                    <svg class="w-4 h-4 md:w-5 md:h-5 text-secondary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-semibold text-sm md:text-base">View Cart</p>
                                            <p class="text-xs text-primary-600">Review your items</p>
                                        </div>
                                        @if($globalCartCount > 0)
                                            <span class="bg-secondary-600 text-white text-xs rounded-full px-2 py-1 font-medium animate-pulse">
                                                {{ $globalCartCount }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </a>
                            <a href="{{ route('orders.index') }}" class="flex items-center p-3 md:p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-gradient-to-r hover:from-accent-50 hover:to-accent-100 hover:text-accent-800 transition-all duration-200 group border border-transparent hover:border-accent-200 hover:shadow-sm">
                                <div class="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-accent-100 to-accent-200 rounded-lg flex items-center justify-center mr-3 md:mr-4 group-hover:from-accent-200 group-hover:to-accent-300 transition-all duration-200">
                                    <svg class="w-4 h-4 md:w-5 md:h-5 text-accent-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm md:text-base">My Orders</p>
                                    <p class="text-xs text-primary-600">Track your purchases</p>
                                </div>
                            </a>
                        @else
                            <a href="{{ route('projects.index') }}" class="flex items-center p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-secondary-50 hover:text-secondary-700 transition-all duration-200 group border border-transparent hover:border-secondary-200">
                                <div class="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-secondary-200 transition-colors duration-200">
                                    <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">My Projects</p>
                                    <p class="text-xs text-primary-500">View project status</p>
                                </div>
                            </a>
                            <a href="{{ route('contact') }}" class="flex items-center p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-accent-50 hover:text-accent-700 transition-all duration-200 group border border-transparent hover:border-accent-200">
                                <div class="w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-accent-200 transition-colors duration-200">
                                    <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">Contact Support</p>
                                    <p class="text-xs text-primary-500">Get help & assistance</p>
                                </div>
                            </a>
                            <a href="{{ route('shop.index') }}" class="flex items-center p-4 text-sm font-medium text-primary-700 rounded-xl hover:bg-success-50 hover:text-success-700 transition-all duration-200 group border border-transparent hover:border-success-200">
                                <div class="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center mr-4 group-hover:bg-success-200 transition-colors duration-200">
                                    <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-medium">Browse Products</p>
                                    <p class="text-xs text-primary-500">Shop our catalog</p>
                                </div>
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100 mb-6">
                <div class="px-6 py-5 border-b border-neutral-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-accent-100 to-accent-200 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-accent-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-primary-900">Recent Activity</h3>
                    </div>
                </div>
                <div class="p-6">
                    @if(count($recent_activity) > 0)
                        <div class="space-y-4">
                            @foreach($recent_activity as $activity)
                                <div class="flex items-start space-x-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors duration-200">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 rounded-full flex items-center justify-center
                                            @if($activity['icon'] === 'shopping-bag') bg-gradient-to-br from-primary-100 to-primary-200
                                            @elseif($activity['icon'] === 'briefcase') bg-gradient-to-br from-secondary-100 to-secondary-200
                                            @else bg-gradient-to-br from-neutral-100 to-neutral-200
                                            @endif">
                                            @if($activity['icon'] === 'shopping-bag')
                                                <svg class="w-4 h-4 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                                </svg>
                                            @elseif($activity['icon'] === 'briefcase')
                                                <svg class="w-4 h-4 text-secondary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                                </svg>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-primary-900">{{ $activity['title'] }}</p>
                                        <p class="text-sm text-neutral-600">{{ $activity['description'] }}</p>
                                        <p class="text-xs text-neutral-500 mt-1">{{ $activity['date']->diffForHumans() }}</p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <a href="{{ $activity['url'] }}" class="p-1 text-neutral-400 hover:text-primary-600 transition-colors duration-200">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="w-12 h-12 bg-neutral-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <p class="text-sm text-neutral-500">No recent activity</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Account Info -->
            <div class="bg-white rounded-xl shadow-soft border border-neutral-100">
                <div class="px-6 py-5 border-b border-neutral-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center">
                            <svg class="w-4 h-4 text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-primary-900">Account Information</h3>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-primary-600">Member since</span>
                            <span class="text-sm font-semibold text-primary-900">{{ auth()->user()->created_at->format('M Y') }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-primary-600">Account type</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                {{ ucfirst($user_type) }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-primary-600">Status</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $quick_stats['account_status'] === 'active' ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800' }}">
                                @if($quick_stats['account_status'] === 'active')
                                    <div class="w-1.5 h-1.5 bg-success-500 rounded-full mr-1.5"></div>
                                @else
                                    <div class="w-1.5 h-1.5 bg-danger-500 rounded-full mr-1.5"></div>
                                @endif
                                {{ ucfirst($quick_stats['account_status']) }}
                            </span>
                        </div>
                        @if(auth()->user()->email_verified_at)
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-primary-600">Email verified</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                    </svg>
                                    Verified
                                </span>
                            </div>
                        @else
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-primary-600">Email status</span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                    </svg>
                                    Unverified
                                </span>
                            </div>
                        @endif
                    </div>

                    <!-- Account Actions -->
                    <div class="mt-6 pt-4 border-t border-neutral-200">
                        <div class="space-y-2">
                            <a href="{{ route('profile.edit') }}" class="flex items-center p-3 text-sm font-medium text-primary-700 rounded-lg hover:bg-primary-50 hover:text-primary-900 transition-all duration-200 group">
                                <svg class="w-4 h-4 text-primary-500 mr-3 group-hover:text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                </svg>
                                Edit Profile
                            </a>
                            <a href="{{ route('addresses.index') }}" class="flex items-center p-3 text-sm font-medium text-primary-700 rounded-lg hover:bg-primary-50 hover:text-primary-900 transition-all duration-200 group">
                                <svg class="w-4 h-4 text-primary-500 mr-3 group-hover:text-primary-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                Manage Addresses
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Dashboard functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Add any dashboard-specific JavaScript here
    });
</script>
@endpush
