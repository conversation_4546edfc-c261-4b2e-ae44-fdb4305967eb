@extends('layouts.app')

@section('title', 'Blog Archive' . ($year ? ' - ' . $year : '') . ($month ? '/' . str_pad($month, 2, '0', STR_PAD_LEFT) : ''))
@section('meta_description', 'Browse our blog archive' . ($year ? ' from ' . $year : '') . ($month ? ' ' . date('F', mktime(0, 0, 0, $month, 1)) : '') . '. Find articles by date and explore our content history.')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home') }}" class="hover:text-white transition-colors">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white transition-colors">Blog</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">Archive</li>
                    @if($year)
                        <li><span class="mx-2">/</span></li>
                        <li class="text-white">{{ $year }}</li>
                    @endif
                    @if($month)
                        <li><span class="mx-2">/</span></li>
                        <li class="text-white">{{ date('F', mktime(0, 0, 0, $month, 1)) }}</li>
                    @endif
                </ol>
            </nav>

            <h1 class="heading-1 text-white mb-6">
                Blog <span class="text-blue-300">Archive</span>
                @if($year || $month)
                    <br>
                    <span class="text-2xl lg:text-3xl">
                        @if($month)
                            {{ date('F', mktime(0, 0, 0, $month, 1)) }} 
                        @endif
                        @if($year)
                            {{ $year }}
                        @endif
                    </span>
                @endif
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                @if($year && $month)
                    Browse all articles published in {{ date('F Y', mktime(0, 0, 0, $month, 1, $year)) }}.
                @elseif($year)
                    Browse all articles published in {{ $year }}.
                @else
                    Browse our complete blog archive organized by date.
                @endif
            </p>
            <div class="text-blue-200">
                {{ $posts->total() }} {{ Str::plural('article', $posts->total()) }} found
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Archive Filter -->
@if(!$year && !$month)
    <section class="py-12 bg-white border-b border-gray-200">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-xl font-semibold text-gray-900 mb-6 text-center">Browse by Date</h2>
                
                @php
                    $archiveData = \App\Models\BlogPost::where('is_published', true)
                        ->where('is_deleted', false)
                        ->selectRaw('YEAR(published_at) as year, MONTH(published_at) as month, COUNT(*) as count')
                        ->groupBy('year', 'month')
                        ->orderBy('year', 'desc')
                        ->orderBy('month', 'desc')
                        ->get()
                        ->groupBy('year');
                @endphp
                
                @if($archiveData->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($archiveData as $year => $months)
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ $year }}</h3>
                                <div class="space-y-2">
                                    @foreach($months as $monthData)
                                        <a href="{{ route('blog.archive', ['year' => $year, 'month' => $monthData->month]) }}" 
                                           class="flex items-center justify-between p-2 rounded hover:bg-white hover:shadow-sm transition-all duration-200">
                                            <span class="text-gray-700">{{ date('F', mktime(0, 0, 0, $monthData->month, 1)) }}</span>
                                            <span class="text-sm text-gray-500 bg-gray-200 px-2 py-1 rounded-full">{{ $monthData->count }}</span>
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    </section>
@endif

<!-- Archive Posts -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                    <article class="blog-card card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="relative overflow-hidden">
                            @if($post->featured_image)
                                <picture>
                                    @if($post->getWebPImageUrl($post->featured_image))
                                        <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @elseif($post->first_gallery_image_url)
                                <picture>
                                    @if($post->getWebPImageUrl($post->gallery_images[0]))
                                        <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-gray-500 to-gray-600 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            @if($post->total_image_count > 1)
                                <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $post->total_image_count }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6 space-y-3">
                            <div class="flex items-center space-x-2">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                                @endif
                                @if($post->is_featured)
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Featured</span>
                                @endif
                                <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ $post->excerpt }}
                            </p>
                            
                            @if($post->services()->count() > 0)
                                <div class="flex flex-wrap gap-1">
                                    @foreach($post->services()->take(2) as $service)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $service->name }}</span>
                                    @endforeach
                                    @if($post->services()->count() > 2)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->count() - 2 }} more</span>
                                    @endif
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between pt-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                                </div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>{{ $post->reading_time }} min read</span>
                                    @if($post->view_count > 0)
                                        <span>•</span>
                                        <span>{{ number_format($post->view_count) }} views</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center mt-12">
                    {{ $posts->appends(['year' => $year, 'month' => $month])->links('pagination::tailwind') }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                    No Articles Found
                    @if($year && $month)
                        for {{ date('F Y', mktime(0, 0, 0, $month, 1, $year)) }}
                    @elseif($year)
                        for {{ $year }}
                    @endif
                </h3>
                <p class="text-gray-600 mb-6">
                    @if($year || $month)
                        Try browsing a different time period or check out our latest articles.
                    @else
                        We're building our content archive. Check back soon for more articles!
                    @endif
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('blog.index') }}" class="btn-secondary">Browse All Articles</a>
                    @if($year || $month)
                        <a href="{{ route('blog.archive') }}" class="btn-primary">View Full Archive</a>
                    @endif
                </div>
            </div>
        @endif
    </div>
</section>

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
@endpush
@endsection
