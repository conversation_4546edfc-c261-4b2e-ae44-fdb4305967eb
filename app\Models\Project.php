<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Project extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'uuid',
        'title',
        'slug',
        'description',
        'content',
        'client_name',
        'client_id',
        'service_id',
        'featured_image',
        'gallery',
        'project_url',
        'start_date',
        'end_date',
        'estimated_hours',
        'actual_hours',
        'hourly_rate',
        'total_amount',
        'currency_code',
        'status',
        'priority',
        'is_featured',
        'is_published',
        'is_deleted',
        'meta_title',
        'meta_description',
        'meta_keywords',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'estimated_hours' => 'decimal:2',
            'actual_hours' => 'decimal:2',
            'hourly_rate' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'gallery' => 'array',
            'is_featured' => 'boolean',
            'is_published' => 'boolean',
            'is_deleted' => 'boolean',
        ];
    }

    /**
     * Scope a query to only include non-deleted projects.
     */
    public function scopeActive($query)
    {
        return $query->where('is_deleted', false);
    }

    /**
     * Scope a query to only include published projects.
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)->where('is_deleted', false);
    }

    /**
     * Scope a query to only include featured projects.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include projects for a specific client.
     */
    public function scopeForClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * Scope a query to only include active projects.
     */
    public function scopeInProgress($query)
    {
        return $query->whereIn('status', ['active', 'in_progress']);
    }

    /**
     * Get the client that owns the project.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(User::class, 'client_id');
    }

    /**
     * Get the service for the project.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the featured image URL.
     */
    public function getFeaturedImageUrlAttribute(): string
    {
        if (!$this->featured_image) {
            return asset('images/projects/placeholder.jpg');
        }

        $imagePath = $this->featured_image;

        // Handle legacy full system paths and normalize
        $originalPath = $imagePath;
        $needsUpdate = false;

        // Handle various legacy path formats
        if (str_contains($imagePath, storage_path())) {
            // Convert full system path to relative path
            $imagePath = str_replace(storage_path('app/public/'), '', $imagePath);
            $imagePath = str_replace('\\', '/', $imagePath); // Normalize path separators
            $needsUpdate = true;
        } elseif (str_starts_with($imagePath, 'C:') || str_starts_with($imagePath, '/')) {
            // Handle other absolute paths - extract just the relative part
            $pathParts = explode('/', str_replace('\\', '/', $imagePath));
            $storageIndex = array_search('storage', $pathParts);
            if ($storageIndex !== false && isset($pathParts[$storageIndex + 1])) {
                $imagePath = implode('/', array_slice($pathParts, $storageIndex + 1));
                $needsUpdate = true;
            }
        }

        // Clean up any double slashes and ensure proper format
        $imagePath = ltrim(str_replace('//', '/', $imagePath), '/');

        // Update the database with the corrected path if needed
        if ($needsUpdate && $imagePath !== $originalPath) {
            try {
                $this->updateQuietly(['featured_image' => $imagePath]);
            } catch (\Exception $e) {
                // Log but don't fail if update fails
                \Log::warning('Failed to update project image path', [
                    'project_id' => $this->id,
                    'original_path' => $originalPath,
                    'new_path' => $imagePath,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return app(\App\Services\ImageService::class)->getImageUrl($imagePath);
    }

    /**
     * Get all project images with URLs.
     */
    public function getAllImagesAttribute(): array
    {
        $images = [];

        // Always include the featured image URL (which includes placeholder logic)
        $images[] = $this->featured_image_url;

        if ($this->gallery && is_array($this->gallery)) {
            $imageService = app(\App\Services\ImageService::class);
            foreach ($this->gallery as $imagePath) {
                $images[] = $imageService->getImageUrl($imagePath);
            }
        }

        return array_unique($images);
    }

    /**
     * Get the primary image URL (alias for featured_image_url).
     */
    public function getPrimaryImageAttribute(): string
    {
        return $this->featured_image_url;
    }

    /**
     * Get the formatted total amount.
     */
    public function getFormattedTotalAttribute(): string
    {
        return 'R' . number_format($this->total_amount, 2);
    }

    /**
     * Get the status badge color.
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'completed' => 'green',
            'active', 'in_progress' => 'blue',
            'on_hold' => 'yellow',
            'cancelled' => 'red',
            'draft' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get the priority badge color.
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'high' => 'red',
            'medium' => 'yellow',
            'low' => 'green',
            default => 'gray',
        };
    }

    /**
     * Check if project is active.
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['active', 'in_progress']);
    }

    /**
     * Check if project is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Get project progress percentage.
     */
    public function getProgressPercentageAttribute(): int
    {
        if ($this->estimated_hours <= 0) {
            return 0;
        }

        $percentage = ($this->actual_hours / $this->estimated_hours) * 100;
        return min(100, max(0, (int) $percentage));
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($project) {
            if (empty($project->uuid)) {
                $project->uuid = Str::uuid();
            }
            if (empty($project->slug)) {
                $project->slug = Str::slug($project->title);
            }
        });

        static::updating(function ($project) {
            if ($project->isDirty('title') && empty($project->slug)) {
                $project->slug = Str::slug($project->title);
            }
        });
    }
}
