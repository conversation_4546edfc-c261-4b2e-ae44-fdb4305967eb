<?php $__env->startSection('title', __('web-development.page_title')); ?>
<?php $__env->startSection('meta_description', __('web-development.meta_description')); ?>
<?php $__env->startSection('meta_keywords', __('web-development.meta_keywords')); ?>

<?php $__env->startPush('structured_data'); ?>
<?php
  $structuredData = [
    "@context" => "https://schema.org",
    "@type" => "Service",
    "name" => "Professional Web Development Services",
    "description" => "Custom web development services including responsive websites, web applications, e-commerce platforms, and CMS solutions using modern technologies like Laravel, React, and Vue.js.",
    "provider" => [
      "@type" => "Organization",
      "name" => __('common.company_name'),
      "url" => url('/en'),
      "logo" => asset('images/logo.png'),
      "address" => [
        "@type" => "PostalAddress",
        "addressCountry" => "ZA",
        "addressRegion" => "South Africa"
      ],
      "contactPoint" => [
        "@type" => "ContactPoint",
        "telephone" => "+27-11-123-4567",
        "contactType" => "customer service",
        "availableLanguage" => ["English", "Afrikaans"]
      ]
    ],
    "areaServed" => [
      "@type" => "Country",
      "name" => "South Africa"
    ],
    "serviceType" => "Web Development",
    "category" => "Technology Services",
    "hasOfferCatalog" => [
      "@type" => "OfferCatalog",
      "name" => "Web Development Services",
      "itemListElement" => [
        [
          "@type" => "Offer",
          "itemOffered" => [
            "@type" => "Service",
            "name" => "Custom Website Development",
            "description" => "Bespoke website development tailored to your business needs"
          ]
        ],
        [
          "@type" => "Offer",
          "itemOffered" => [
            "@type" => "Service",
            "name" => "E-commerce Development",
            "description" => "Full-featured online stores with payment integration"
          ]
        ],
        [
          "@type" => "Offer",
          "itemOffered" => [
            "@type" => "Service",
            "name" => "Web Application Development",
            "description" => "Complex web applications with advanced functionality"
          ]
        ],
        [
          "@type" => "Offer",
          "itemOffered" => [
            "@type" => "Service",
            "name" => "CMS Development",
            "description" => "Content management systems for easy website updates"
          ]
        ]
      ]
    ],
    "offers" => [
      "@type" => "Offer",
      "availability" => "https://schema.org/InStock",
      "priceRange" => "R5000-R50000",
      "priceCurrency" => "ZAR"
    ],
    "aggregateRating" => [
      "@type" => "AggregateRating",
      "ratingValue" => "4.8",
      "reviewCount" => "42",
      "bestRating" => "5"
    ]
  ];
?>
<script type="application/ld+json">
  <?php echo json_encode($structuredData, JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE); ?>

</script>
<?php $__env->stopPush(); ?>



<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="heading-1 text-white mb-6">
                    <?php echo e(__('web-development.hero_title')); ?>

                </h1>
                <p class="text-lead text-white mb-8 opacity-90">
                    <?php echo e(__('web-development.hero_description')); ?>

                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="btn-primary bg-white text-blue-600 font-semibold hover:bg-gray-50 hover:shadow-lg transition-all duration-300">
                        <?php echo e(__('web-development.get_quote')); ?>

                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="<?php echo e(route('projects.index', ['locale' => app()->getLocale()])); ?>" class="btn-outline bg-white border-white border-2 text-white font-semibold hover:bg-white hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                        <?php echo e(__('web-development.view_portfolio')); ?>

                    </a>
                </div>
            </div>
            
            <div class="relative">
                <div class="relative z-10">
                    <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-8">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                            </div>
                            <div class="bg-gray-800 rounded p-4 text-green-400 font-mono text-sm">
                                <div>&lt;html&gt;</div>
                                <div class="ml-4">&lt;head&gt;</div>
                                <div class="ml-8">&lt;title&gt;Your Website&lt;/title&gt;</div>
                                <div class="ml-4">&lt;/head&gt;</div>
                                <div class="ml-4">&lt;body&gt;</div>
                                <div class="ml-8">&lt;h1&gt;Hello World!&lt;/h1&gt;</div>
                                <div class="ml-4">&lt;/body&gt;</div>
                                <div>&lt;/html&gt;</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Services Overview -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                <?php echo __('web-development.modern_solutions_title'); ?>

            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                <?php echo e(__('web-development.modern_solutions_description')); ?>

            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Progressive Web Apps -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.pwa_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.pwa_description')); ?>

                </p>
            </div>
            
            <!-- E-commerce Platforms -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.ecommerce_platforms_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.ecommerce_platforms_description')); ?>

                </p>
            </div>
            
            <!-- professional Web Applications -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.ai_web_apps_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.ai_web_apps_description')); ?>

                </p>
            </div>
            
            <!-- Landing Pages -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.landing_pages_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.landing_pages_description')); ?>

                </p>
            </div>
            
            <!-- Portfolio Websites -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.portfolio_websites_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.portfolio_websites_description')); ?>

                </p>
            </div>
            
            <!-- Headless CMS Solutions -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.headless_cms_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.headless_cms_description')); ?>

                </p>
            </div>

            <!-- API Development -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.api_development_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.api_development_description')); ?>

                </p>
            </div>

            <!-- WordPress Development -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.wordpress_development_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.wordpress_development_description')); ?>

                </p>
            </div>
        </div>
    </div>
</section>

<!-- Technologies Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                <?php echo __('web-development.technologies_title'); ?>

            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                <?php echo e(__('web-development.technologies_description')); ?>

            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            <!-- React -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-500 font-bold text-lg">⚛</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">React</h4>
            </div>

            <!-- Vue.js -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-green-500 font-bold text-lg">V</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Vue.js</h4>
            </div>

            <!-- Next.js -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-black font-bold text-lg">▲</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Next.js</h4>
            </div>

            <!-- Laravel -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-red-500 font-bold text-lg">L</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Laravel</h4>
            </div>

            <!-- Node.js -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-green-600 font-bold text-lg">⬢</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Node.js</h4>
            </div>

            <!-- TypeScript -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-600 font-bold text-lg">TS</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">TypeScript</h4>
            </div>

            <!-- JAMstack -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-purple-600 font-bold text-lg">JAM</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">JAMstack</h4>
            </div>

            <!-- GraphQL -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-pink-500 font-bold text-lg">GQL</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">GraphQL</h4>
            </div>

            <!-- Docker -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-400 font-bold text-lg">🐳</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Docker</h4>
            </div>

            <!-- AWS -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-orange-500 font-bold text-lg">AWS</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">AWS</h4>
            </div>

            <!-- MongoDB -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-green-500 font-bold text-lg">🍃</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">MongoDB</h4>
            </div>

            <!-- WordPress -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-600 font-bold text-lg">W</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">WordPress</h4>
            </div>

            <!-- Django -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-green-700 font-bold text-lg">DJ</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">Django</h4>
            </div>

            <!-- ASP.NET -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-purple-600 font-bold text-lg">.NET</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">ASP.NET</h4>
            </div>

            <!-- JSP -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-orange-600 font-bold text-lg">JSP</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">JSP</h4>
            </div>

            <!-- MSSQL Server -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-red-600 font-bold text-lg">MS</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">MSSQL Server</h4>
            </div>

            <!-- MySQL -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-600 font-bold text-lg">🐬</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">MySQL</h4>
            </div>

            <!-- PostgreSQL -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-blue-800 font-bold text-lg">🐘</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">PostgreSQL</h4>
            </div>

            <!-- AI/ML -->
            <div class="text-center">
                <div class="w-16 h-16 bg-white rounded-lg shadow-md flex items-center justify-center mx-auto mb-3">
                    <span class="text-indigo-600 font-bold text-lg">🤖</span>
                </div>
                <h4 class="text-sm font-medium text-gray-900">AI/ML</h4>
            </div>
        </div>
    </div>
</section>

<!-- WordPress Development Section -->
<section class="py-20 relative overflow-hidden" style="background: linear-gradient(135deg, #0073aa 0%, #005177 50%, #21759b 100%);">


    <!-- Subtle Pattern Overlay -->
    <div class="absolute inset-0 opacity-5" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;"></div>

    <!-- Animated Background Elements -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute top-10 left-10 w-32 h-32 bg-white rounded-full animate-pulse"></div>
        <div class="absolute top-40 right-20 w-20 h-20 bg-white rounded-full animate-bounce" style="animation-delay: 1s; animation-duration: 2s;"></div>
        <div class="absolute bottom-20 left-1/4 w-16 h-16 bg-white rounded-full animate-ping" style="animation-delay: 2s; animation-duration: 3s;"></div>
        <div class="absolute bottom-40 right-1/3 w-24 h-24 bg-white rounded-full animate-pulse" style="animation-delay: 0.5s; animation-duration: 2.5s;"></div>
        <div class="absolute top-1/2 left-1/2 w-12 h-12 bg-white rounded-full animate-pulse" style="animation-delay: 1.5s; animation-duration: 2s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-flex items-center justify-center w-24 h-24 bg-white rounded-full mb-8 shadow-2xl transform hover:scale-110 transition-transform duration-300">
                <svg class="w-14 h-14" viewBox="0 0 24 24" fill="#0073aa">
                    <path d="M21.469 6.825c.84 1.537 1.318 3.3 1.318 5.175 0 3.979-2.156 7.456-5.363 9.325l3.295-9.527c.615-1.54.82-2.771.82-3.864 0-.405-.026-.78-.07-1.11m-7.981.105c.647-.03 1.232-.105 1.232-.105.582-.075.514-.93-.067-.899 0 0-1.755.135-2.88.135-1.064 0-2.85-.135-2.85-.135-.584-.03-.661.854-.075.899 0 0 .584.075 1.195.105l1.777 4.877-2.5 7.51-4.158-12.387c.647-.03 1.232-.105 1.232-.105.582-.075.516-.93-.065-.899 0 0-1.756.135-2.88.135-.202 0-.438-.008-.69-.015C4.911 2.015 8.235 0 12.017 0c2.926 0 5.587 1.122 7.571 2.958-.048-.003-.094-.008-.144-.008-1.064 0-1.818.93-1.818 1.93 0 .895.514 1.65 1.066 2.54.416.72.899 1.65.899 2.99 0 .93-.357 2.017-.832 3.512l-1.103 3.707-3.994-11.99M12.017 24C5.624 24.001 .403 18.78.403 12.387S5.624.774 12.017.774s11.614 5.221 11.614 11.613S18.41 24.001 12.017 24"/>
                </svg>
            </div>
            <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6 tracking-tight">
                <?php echo e(__('web-development.wordpress_excellence_title')); ?>

            </h2>
            <p class="text-xl text-gray-100 max-w-4xl mx-auto leading-relaxed font-light">
                <?php echo e(__('web-development.wordpress_excellence_description')); ?>

            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <!-- Custom Theme Development -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50 animate-fade-in-up">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-blue-100 transition-colors duration-300"><?php echo e(__('web-development.custom_theme_development_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.custom_theme_development_description')); ?>

                </p>
            </div>

            <!-- Plugin Development -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-green-100 transition-colors duration-300"><?php echo e(__('web-development.custom_plugin_development_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.custom_plugin_development_description')); ?>

                </p>
            </div>

            <!-- WooCommerce Solutions -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-purple-100 transition-colors duration-300"><?php echo e(__('web-development.woocommerce_solutions_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.woocommerce_solutions_description')); ?>

                </p>
            </div>

            <!-- WordPress Optimization -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-yellow-100 transition-colors duration-300"><?php echo e(__('web-development.performance_optimization_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.performance_optimization_description')); ?>

                </p>
            </div>

            <!-- Security & Maintenance -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-red-100 transition-colors duration-300"><?php echo e(__('web-development.security_maintenance_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.security_maintenance_description')); ?>

                </p>
            </div>

            <!-- Headless WordPress -->
            <div class="group bg-gray-900 bg-opacity-40 backdrop-blur-md rounded-xl p-8 hover:bg-opacity-60 hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-300 border-opacity-30 hover:border-opacity-50">
                <div class="w-16 h-16 bg-white rounded-xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
                    <svg class="w-8 h-8 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4 group-hover:text-indigo-100 transition-colors duration-300"><?php echo e(__('web-development.headless_wordpress_title')); ?></h3>
                <p class="text-blue-200 leading-relaxed group-hover:text-white transition-colors duration-300">
                    <?php echo e(__('web-development.headless_wordpress_description')); ?>

                </p>
            </div>
        </div>

        <div class="text-center">
            <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="group inline-flex items-center px-10 py-5 bg-white text-blue-600 font-bold text-lg rounded-2xl hover:bg-gray-50 hover:shadow-2xl hover:scale-105 transition-all duration-300 shadow-xl">
                <?php echo e(__('web-development.start_wordpress_project')); ?>

                <svg class="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </div>
</section>

<!-- Web App Enhancement Features -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                <?php echo __('web-development.enhance_title'); ?>

            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                <?php echo e(__('web-development.enhance_description')); ?>

            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Live Chat & Chatbots -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.live_chat_chatbots_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.live_chat_chatbots_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.live_chat_features.Real-time messaging')); ?></li>
                    <li>• <?php echo e(__('web-development.live_chat_features.AI-powered responses')); ?></li>
                    <li>• <?php echo e(__('web-development.live_chat_features.Multi-language support')); ?></li>
                    <li>• <?php echo e(__('web-development.live_chat_features.Integration with CRM systems')); ?></li>
                </ul>
            </div>

            <!-- Inventory Management System -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.inventory_system_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.inventory_system_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.inventory_features.Stock level tracking')); ?></li>
                    <li>• <?php echo e(__('web-development.inventory_features.Automated alerts')); ?></li>
                    <li>• <?php echo e(__('web-development.inventory_features.Barcode integration')); ?></li>
                    <li>• <?php echo e(__('web-development.inventory_features.Supplier management')); ?></li>
                </ul>
            </div>

            <!-- E-commerce Features -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.ecommerce_enhancement_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.ecommerce_enhancement_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.ecommerce_features_list.Shopping cart functionality')); ?></li>
                    <li>• <?php echo e(__('web-development.ecommerce_features_list.Payment gateway integration')); ?></li>
                    <li>• <?php echo e(__('web-development.ecommerce_features_list.Order tracking system')); ?></li>
                    <li>• <?php echo e(__('web-development.ecommerce_features_list.Customer account portal')); ?></li>
                </ul>
            </div>

            <!-- User Authentication & Authorization -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.user_authentication_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.user_authentication_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.user_auth_features.Multi-factor authentication')); ?></li>
                    <li>• <?php echo e(__('web-development.user_auth_features.Social login integration')); ?></li>
                    <li>• <?php echo e(__('web-development.user_auth_features.Role-based permissions')); ?></li>
                    <li>• <?php echo e(__('web-development.user_auth_features.Password recovery system')); ?></li>
                </ul>
            </div>

            <!-- Analytics & Reporting Dashboard -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.analytics_reporting_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.analytics_reporting_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.analytics_features.Real-time data visualization')); ?></li>
                    <li>• <?php echo e(__('web-development.analytics_features.Custom report generation')); ?></li>
                    <li>• <?php echo e(__('web-development.analytics_features.KPI tracking')); ?></li>
                    <li>• <?php echo e(__('web-development.analytics_features.Export functionality')); ?></li>
                </ul>
            </div>

            <!-- Content Management System -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.content_management_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.content_management_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.cms_features.WYSIWYG editor')); ?></li>
                    <li>• <?php echo e(__('web-development.cms_features.Media library management')); ?></li>
                    <li>• <?php echo e(__('web-development.cms_features.Content scheduling')); ?></li>
                    <li>• <?php echo e(__('web-development.cms_features.SEO optimization tools')); ?></li>
                </ul>
            </div>

            <!-- Booking & Appointment System -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.booking_appointment_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.booking_appointment_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.booking_features.Calendar integration')); ?></li>
                    <li>• <?php echo e(__('web-development.booking_features.Automated notifications')); ?></li>
                    <li>• <?php echo e(__('web-development.booking_features.Payment processing')); ?></li>
                    <li>• <?php echo e(__('web-development.booking_features.Recurring appointments')); ?></li>
                </ul>
            </div>

            <!-- Email Marketing Integration -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.email_marketing_integration_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.email_marketing_integration_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.email_marketing_features.Automated campaigns')); ?></li>
                    <li>• <?php echo e(__('web-development.email_marketing_features.Subscriber management')); ?></li>
                    <li>• <?php echo e(__('web-development.email_marketing_features.Email templates')); ?></li>
                    <li>• <?php echo e(__('web-development.email_marketing_features.Performance tracking')); ?></li>
                </ul>
            </div>

            <!-- Search & Filtering System -->
            <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.advanced_search_filtering_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed mb-4">
                    <?php echo e(__('web-development.advanced_search_filtering_description')); ?>

                </p>
                <ul class="text-xs text-gray-500 space-y-1">
                    <li>• <?php echo e(__('web-development.advanced_search_features.Elasticsearch integration')); ?></li>
                    <li>• <?php echo e(__('web-development.advanced_search_features.Faceted search filters')); ?></li>
                    <li>• <?php echo e(__('web-development.advanced_search_features.Autocomplete suggestions')); ?></li>
                    <li>• <?php echo e(__('web-development.advanced_search_features.Search analytics')); ?></li>
                </ul>
            </div>
        </div>

        <div class="text-center mt-12">
            <div class="bg-blue-50 rounded-lg p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-gray-900 mb-4"><?php echo e(__('web-development.enhance_cta_title')); ?></h3>
                <p class="text-gray-600 mb-6">
                    <?php echo e(__('web-development.enhance_cta_description')); ?>

                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('contact', ['locale' => app()->getLocale()])); ?>" class="btn-primary">
                        <?php echo e(__('web-development.get_custom_quote')); ?>

                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="<?php echo e(route('projects.index', ['locale' => app()->getLocale()])); ?>" class="btn-outline">
                        <?php echo e(__('web-development.view_enhancement_examples')); ?>

                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 2025 Web Development Trends -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                <?php echo __('web-development.trends_title'); ?>

            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                <?php echo e(__('web-development.trends_description')); ?>

            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Serverless Architecture -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.serverless_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.serverless_description')); ?>

                </p>
            </div>

            <!-- Voice Search Optimization -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.voice_search_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.voice_search_description')); ?>

                </p>
            </div>

            <!-- WebAssembly Integration -->
            <div class="card-hover">
                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-3"><?php echo e(__('web-development.webassembly_title')); ?></h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    <?php echo e(__('web-development.webassembly_description')); ?>

                </p>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                <?php echo __('web-development.why_choose_title'); ?>

            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                <?php echo e(__('web-development.why_choose_description')); ?>

            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">⚡</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e(__('web-development.lightning_fast_title')); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo e(__('web-development.lightning_fast_description')); ?></p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🔒</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e(__('web-development.secure_reliable_title')); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo e(__('web-development.secure_reliable_description')); ?></p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📱</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e(__('web-development.mobile_first_title')); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo e(__('web-development.mobile_first_description')); ?></p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🚀</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo e(__('web-development.seo_optimized_title')); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo e(__('web-development.seo_optimized_description')); ?></p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                Frequently Asked <span class="text-blue-600">Questions</span>
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                Get answers to common questions about our web development services and process.
            </p>
        </div>

        <div class="max-w-4xl mx-auto">
            <div class="space-y-6">
                <!-- FAQ 1 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">How long does it take to develop a custom website?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        The timeline depends on the complexity and scope of your project. A simple business website typically takes 2-4 weeks, while complex web applications can take 8-16 weeks. We provide detailed project timelines during our initial consultation and keep you updated throughout the development process.
                    </p>
                </div>

                <!-- FAQ 2 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">What technologies do you use for web development?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        We use modern, industry-standard technologies including Laravel (PHP), React.js, Vue.js, Node.js, and TypeScript for development. For databases, we work with MySQL, PostgreSQL, and MongoDB. All our websites are built with responsive design, SEO optimization, and performance in mind.
                    </p>
                </div>

                <!-- FAQ 3 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Do you provide ongoing maintenance and support?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Yes, we offer comprehensive maintenance packages that include security updates, performance monitoring, content updates, backup management, and technical support. Our maintenance plans ensure your website remains secure, fast, and up-to-date with the latest web standards.
                    </p>
                </div>

                <!-- FAQ 4 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Can you help with SEO and digital marketing?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Absolutely! We build all websites with SEO best practices from the ground up, including proper schema markup, optimized page speed, and mobile-first design. We also offer comprehensive digital marketing services including SEO campaigns, Google Ads management, and social media marketing.
                    </p>
                </div>

                <!-- FAQ 5 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">What is your pricing structure?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Our pricing is project-based and depends on your specific requirements. Simple websites start from R5,000, while complex web applications can range from R20,000 to R100,000+. We provide detailed quotes after understanding your needs and always deliver transparent pricing with no hidden costs.
                    </p>
                </div>

                <!-- FAQ 6 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Do you work with international clients?</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Yes, while we're based in South Africa, we work with clients globally. We have experience with international projects and can accommodate different time zones, currencies, and business requirements. Our remote collaboration tools ensure smooth communication regardless of location.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->startPush('structured_data'); ?>
<script type="application/ld+json">
{
  "<?php $__contextArgs = [];
if (context()->has($__contextArgs[0])) :
if (isset($value)) { $__contextPrevious[] = $value; }
$value = context()->get($__contextArgs[0]); ?>": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": [
    {
      "@type": "Question",
      "name": "How long does it take to develop a custom website?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "The timeline depends on the complexity and scope of your project. A simple business website typically takes 2-4 weeks, while complex web applications can take 8-16 weeks. We provide detailed project timelines during our initial consultation and keep you updated throughout the development process."
      }
    },
    {
      "@type": "Question",
      "name": "What technologies do you use for web development?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "We use modern, industry-standard technologies including Laravel (PHP), React.js, Vue.js, Node.js, and TypeScript for development. For databases, we work with MySQL, PostgreSQL, and MongoDB. All our websites are built with responsive design, SEO optimization, and performance in mind."
      }
    },
    {
      "@type": "Question",
      "name": "Do you provide ongoing maintenance and support?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, we offer comprehensive maintenance packages that include security updates, performance monitoring, content updates, backup management, and technical support. Our maintenance plans ensure your website remains secure, fast, and up-to-date with the latest web standards."
      }
    },
    {
      "@type": "Question",
      "name": "Can you help with SEO and digital marketing?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Absolutely! We build all websites with SEO best practices from the ground up, including proper schema markup, optimized page speed, and mobile-first design. We also offer comprehensive digital marketing services including SEO campaigns, Google Ads management, and social media marketing."
      }
    },
    {
      "@type": "Question",
      "name": "What is your pricing structure?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Our pricing is project-based and depends on your specific requirements. Simple websites start from R5,000, while complex web applications can range from R20,000 to R100,000+. We provide detailed quotes after understanding your needs and always deliver transparent pricing with no hidden costs."
      }
    },
    {
      "@type": "Question",
      "name": "Do you work with international clients?",
      "acceptedAnswer": {
        "@type": "Answer",
        "text": "Yes, while we're based in South Africa, we work with clients globally. We have experience with international projects and can accommodate different time zones, currencies, and business requirements. Our remote collaboration tools ensure smooth communication regardless of location."
      }
    }
  ]
}
</script>
<?php $__env->stopPush(); ?>

<!-- Structured Data -->
<script type="application/ld+json">
{
    "@type": "Service",
    "name": "Web Development Services",
    "description": "Professional web development services specializing in progressive web apps, headless CMS, and serverless architecture.",
    "provider": {
        "@type": "Organization",
        "name": "<?php echo e(config('app.name')); ?>",
        "url": "<?php echo e(url('/')); ?>"
    },
    "serviceType": "Web Development",
    "areaServed": {
        "@type": "Country",
        "name": "South Africa"
    },
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Web Development Services",
        "itemListElement": [
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Progressive Web Apps Development",
                    "description": "App-like web experiences with offline functionality and push notifications"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "professional Web Applications",
                    "description": "Intelligent web applications with machine learning integration and automation"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "Headless CMS Solutions",
                    "description": "Modern content management with API-first architecture"
                }
            },
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "E-commerce Development",
                    "description": "Complete online stores with payment processing and inventory management"
                }
            }
        ]
    },
    "offers": {
        "@type": "Offer",
        "availability": "https://schema.org/InStock",
        "priceCurrency": "ZAR",
        "priceRange": "Contact for quote"
    }
}
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/pages/services/web-development.blade.php ENDPATH**/ ?>