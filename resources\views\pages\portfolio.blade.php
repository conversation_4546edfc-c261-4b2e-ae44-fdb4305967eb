@extends('layouts.app')

@section('title', __('projects.hero_title') . ' - ' . __('common.company_name'))
@section('meta_description', __('projects.hero_description'))
@section('meta_keywords', __('projects.projects_list_title') . ', ' . __('common.company_name'))

@push('structured_data')
<script type="application/ld+json">
{
  "@@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": "Our Portfolio - Digital Projects",
  "description": "Explore our portfolio of successful digital projects including websites, mobile apps, and e-commerce solutions.",
  "url": @json(route('projects.index', ['locale' => app()->getLocale()])),
  "mainEntity": {
    "@type": "ItemList",
    "numberOfItems": {{ $projects->total() }},
    "itemListElement": [
      @foreach($projects as $index => $project)
      {
        "@type": "CreativeWork",
        "position": {{ $index + 1 }},
        "name": "{{ $project->title }}",
        "description": "{{ $project->description }}",
        "url": "{{ route('projects.show', ['project' => $project->slug, 'locale' => app()->getLocale()]) }}",
        @if($project->featured_image)
        "image": "{{ asset('storage/' . $project->featured_image) }}",
        @endif
        "dateCreated": "{{ $project->created_at->format('Y-m-d') }}",
        "creator": {
          "@type": "Organization",
          "name": "{{ __('common.company_name') }}"
        }
      }@if(!$loop->last),@endif
      @endforeach
    ]
  }
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <h1 class="heading-1 text-white mb-6">
                {!! __('projects.hero_title') !!}
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                {{ __('projects.hero_description') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('apply.project', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                    {{ __('projects.start_your_project') }}
                    <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Filter Section -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4">
            <button class="filter-btn active" data-filter="all">{{ __('projects.projects_list_title') }}</button>
            <button class="filter-btn" data-filter="web-development">{{ __('projects.filter_web_development') }}</button>
            <button class="filter-btn" data-filter="mobile-apps">{{ __('projects.filter_mobile_app_development') }}</button>
            <button class="filter-btn" data-filter="ecommerce">{{ __('projects.filter_ecommerce_development') }}</button>
            <button class="filter-btn" data-filter="branding">{{ __('projects.filter_branding') }}</button>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($projects->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
                @foreach($projects as $project)
                <div class="project-card card-hover" data-category="{{ strtolower(str_replace(' ', '-', $project->service->name ?? 'general')) }}">
                    <div class="relative overflow-hidden rounded-lg mb-6">
                        @if($project->featured_image)
                            <img src="{{ asset('storage/' . $project->featured_image) }}" alt="{{ $project->title }}" class="w-full h-48 object-cover hover-lift">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                            </div>
                        @endif
                        <div class="absolute inset-0 bg-blue-600 bg-opacity-0 hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center">
                            <a href="{{ route('projects.show', ['project' => $project->slug, 'locale' => app()->getLocale()]) }}" class="btn-primary opacity-0 hover:opacity-100 transition-opacity duration-300">
                                {{ __('projects.view_project') }}
                            </a>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            @if($project->service)
                                <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $project->service->name }}</span>
                            @endif
                            @if($project->is_featured)
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">{{ __('projects.featured') }}</span>
                            @endif
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900">{{ $project->title }}</h3>
                        <p class="text-gray-600 text-sm leading-relaxed">
                            {{ Str::limit($project->description, 120) }}
                        </p>
                        <div class="flex items-center text-sm text-gray-500">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            Completed: {{ $project->end_date ? $project->end_date->format('F Y') : $project->created_at->format('F Y') }}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="mt-12">
                {{ $projects->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('projects.no_projects') }}</h3>
                <p class="text-gray-600 mb-6">{{ __('projects.projects_list_description') }}</p>
                <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary">
                    {{ __('projects.cta_title') }}
                </a>
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            {{ __('projects.cta_title') }}
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            {{ __('projects.cta_description') }}
        </p>
        <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
            {{ __('projects.get_in_touch') }}
            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
        </a>
        <a href="{{ route('services.index', ['locale' => app()->getLocale()]) }}" class="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
            {{ __('common.services') }}
        </a>
    </div>
</section>

@push('styles')
<style>
.filter-btn {
    @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors;
    @apply bg-gray-100 text-gray-600 hover:bg-gray-200;
}

.filter-btn.active {
    @apply bg-blue-600 text-white;
}

.project-card {
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-4px);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const projectCards = document.querySelectorAll('.project-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter projects
            projectCards.forEach(card => {
                const categories = card.getAttribute('data-category');
                
                if (filter === 'all' || categories.includes(filter)) {
                    card.style.display = 'block';
                    card.classList.add('animate-fade-in');
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});
</script>
@endpush
@endsection
