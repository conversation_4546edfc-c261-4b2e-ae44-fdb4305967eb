@extends('layouts.app')

@section('title', $service->name . ' - Blog Articles')
@section('meta_description', 'Read our latest articles and insights about ' . $service->name . '. Expert tips, tutorials, and industry trends.')
@section('meta_keywords', $service->name . ', blog, articles, tutorials, insights')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="hover:text-white transition-colors">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white transition-colors">Blog</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('services.show', $service->slug) }}" class="hover:text-white transition-colors">{{ $service->name }}</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">Articles</li>
                </ol>
            </nav>

            <h1 class="heading-1 text-white mb-6">
                {{ $service->name }} <span class="text-blue-300">Articles</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                {{ __('blog.service_description', ['service' => $service->name]) }}
            </p>
            <div class="flex items-center justify-center space-x-6 text-blue-200">
                <div>{{ $posts->total() }} {{ trans_choice('blog.article_count', $posts->total()) }}</div>
                <div>•</div>
                <div>{{ __('blog.expert_insights') }}</div>
                <div>•</div>
                <div>{{ __('blog.industry_trends') }}</div>
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Service Info Banner -->
<section class="py-12 bg-white border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <div class="lg:col-span-2">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About {{ $service->name }}</h2>
                    <p class="text-gray-600 leading-relaxed">
                        {{ $service->description }}
                    </p>
                </div>
                <div class="text-center lg:text-right">
                    <a href="{{ route('services.show', $service->slug) }}" 
                       class="btn-primary inline-flex items-center">
                        Learn More About {{ $service->name }}
                        <svg class="ml-2 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Posts Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                    <article class="blog-card card-hover bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="relative overflow-hidden">
                            @if($post->featured_image)
                                <picture>
                                    @if($post->getWebPImageUrl($post->featured_image))
                                        <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @elseif($post->first_gallery_image_url)
                                <picture>
                                    @if($post->getWebPImageUrl($post->gallery_images[0]))
                                        <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            @if($post->total_image_count > 1)
                                <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $post->total_image_count }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6 space-y-3">
                            <div class="flex items-center space-x-2">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                                @endif
                                @if($post->is_featured)
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-600 text-xs rounded">Featured</span>
                                @endif
                                <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ $post->excerpt }}
                            </p>
                            
                            @if($post->services()->count() > 1)
                                <div class="flex flex-wrap gap-1">
                                    @foreach($post->services()->where('id', '!=', $service->id)->take(2) as $relatedService)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $relatedService->name }}</span>
                                    @endforeach
                                    @if($post->services()->where('id', '!=', $service->id)->count() > 2)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->where('id', '!=', $service->id)->count() - 2 }} {{ __('blog.more') }}</span>
                                    @endif
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between pt-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                                </div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>{{ $post->reading_time }} min read</span>
                                    @if($post->view_count > 0)
                                        <span>•</span>
                                        <span>{{ number_format($post->view_count) }} views</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center mt-12">
                    {{ $posts->links('pagination::tailwind') }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('blog.no_articles_title', ['service' => $service->name]) }}</h3>
                <p class="text-gray-600 mb-6">{{ __('blog.no_articles_description', ['service' => $service->name]) }}</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('blog.index') }}" class="btn-secondary">
                        {{ __('blog.browse_all_articles') }}
                    </a>
                    <a href="{{ route('services.show', $service->slug) }}" class="btn-primary">
                        {{ __('blog.learn_about_service', ['service' => $service->name]) }}
                    </a>
                </div>
            </div>
        @endif
    </div>
</section>

<!-- Related Services -->
@php
    $relatedServices = \App\Models\Service::active()
        ->where('id', '!=', $service->id)
        ->limit(6)
        ->get();
@endphp

@if($relatedServices->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="heading-2 mb-4">
                    {!! __('blog.explore_other_services') !!}
                </h2>
                <p class="text-gray-600">{{ __('blog.discover_more_articles') }}</p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                @foreach($relatedServices as $relatedService)
                    <a href="{{ route('blog.service', $relatedService->slug) }}" 
                       class="group p-4 bg-gray-50 rounded-lg hover:bg-blue-50 hover:border-blue-200 border border-transparent transition-all duration-200 text-center">
                        <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors mb-1 text-sm">
                            {{ $relatedService->name }}
                        </h3>
                        <p class="text-xs text-gray-500">
                            {{ __('blog.view_articles') }}
                        </p>
                    </a>
                @endforeach
            </div>
        </div>
    </section>
@endif

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
@endpush
@endsection
