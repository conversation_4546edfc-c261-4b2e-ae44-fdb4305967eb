<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <?php if(auth()->guard()->check()): ?>
    <meta name="user" content="<?php echo e(json_encode([
        'id' => auth()->user()->id,
        'name' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
        'first_name' => auth()->user()->first_name,
        'last_name' => auth()->user()->last_name,
        'email' => auth()->user()->email
    ])); ?>">
    <?php endif; ?>

    <!-- SEO Meta Tags -->
    <title><?php echo $__env->yieldContent('title', __('common.company_name') . ' - ' . __('common.company_tagline')); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', __('common.company_description')); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', 'digital agency, web development, mobile apps, e-commerce'); ?>">
    <meta name="author" content="<?php echo e(__('common.company_name')); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $__env->yieldContent('og_title', __('common.company_name')); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('og_description', __('common.company_description')); ?>">
    <meta property="og:image" content="<?php echo $__env->yieldContent('og_image', asset('images/og-image.jpg')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?php echo e(__('common.company_name')); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $__env->yieldContent('twitter_title', __('common.company_name')); ?>">
    <meta name="twitter:description" content="<?php echo $__env->yieldContent('twitter_description', __('common.company_description')); ?>">
    <meta name="twitter:image" content="<?php echo $__env->yieldContent('twitter_image', asset('images/twitter-image.jpg')); ?>">

    <!-- Hreflang Tags for SEO -->
    <link rel="alternate" hreflang="en" href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('en')); ?>">
    <link rel="alternate" hreflang="fr" href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('fr')); ?>">
    <link rel="alternate" hreflang="es" href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('es')); ?>">
    <link rel="alternate" hreflang="x-default" href="<?php echo e(App\Http\Middleware\LocalizationMiddleware::switchLocaleUrl('en')); ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('images/apple-touch-icon.png')); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('images/favicon-32x32.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('images/favicon-16x16.png')); ?>">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://www.google-analytics.com">
    <link rel="preconnect" href="https://www.googletagmanager.com">

    <!-- Preload critical assets -->
    <link rel="preload" href="<?php echo e(asset('js/chat-widget-standalone.js')); ?>" as="script">
    <link rel="preload" as="image" href="<?php echo e(asset('images/hero-illustration.svg')); ?>">
    <link rel="preload" as="image" href="<?php echo e(asset('images/about-illustration.svg')); ?>">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
    
    <!-- Structured Data -->
    <?php echo $__env->yieldPushContent('structured_data'); ?>
</head>
<body class="font-inter antialiased bg-white text-gray-900 selection:bg-blue-100 selection:text-blue-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>
    
    <!-- Language & Currency Switcher (Mobile) -->
    <div id="mobile-switchers" class="fixed top-0 left-0 w-full bg-gray-50 border-b border-gray-200 z-40 transform -translate-y-full transition-transform duration-300 lg:hidden">
        <div class="container mx-auto px-4 py-2 flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <!-- Language Switcher -->
                <div class="relative">
                    <select class="text-sm border-0 bg-transparent focus:ring-0" onchange="changeLanguage(this.value)">
                        <option value="en" <?php echo e(app()->getLocale() === 'en' ? 'selected' : ''); ?>>English</option>
                        <option value="fr" <?php echo e(app()->getLocale() === 'fr' ? 'selected' : ''); ?>>Français</option>
                        <option value="es" <?php echo e(app()->getLocale() === 'es' ? 'selected' : ''); ?>>Español</option>
                    </select>
                </div>
                
                <!-- Currency Switcher -->
                <div class="relative">
                    <select class="text-sm border-0 bg-transparent focus:ring-0" onchange="changeCurrency(this.value)">
                        <option value="ZAR">ZAR (R)</option>
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Header -->
    <?php echo $__env->make('partials.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <!-- Main Content -->
    <main id="main-content" class="min-h-screen">
        <?php echo $__env->yieldContent('content'); ?>
    </main>
    
    <!-- Footer -->
    <?php echo $__env->make('partials.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 hidden">
        <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-4 text-gray-600"><?php echo e(__('common.loading')); ?></p>
        </div>
    </div>
    
    <!-- Toast Notifications -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Chat Widget (will be initialized by JavaScript) -->
    <div id="chat-widget-container"></div>

    <!-- Debug Script -->
    <script>
        console.log('Inline script loaded');
        console.log('Current URL:', window.location.href);
        console.log('Pathname:', window.location.pathname);
    </script>

    <!-- Inline Chat Widget Script -->
    <script src="<?php echo e(asset('js/chat-widget-standalone.js')); ?>"></script>

    <!-- Debug Script After -->
    <!-- <script>
        console.log('After chat widget script');
        setTimeout(() => {
            console.log('Chat widget after timeout:', window.chatWidget);
            console.log('Toggle function:', window.toggleChat);

            // Add test button if not in admin
            if (!window.location.pathname.includes('/admin')) {
                const testBtn = document.createElement('button');
                testBtn.textContent = 'Test Chat Toggle';
                testBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 10000; background: red; color: white; padding: 10px; border: none; border-radius: 5px; cursor: pointer;';
                testBtn.onclick = function() {
                    console.log('Test button clicked');
                    if (window.toggleChat) {
                        window.toggleChat();
                    } else {
                        console.error('toggleChat function not found');
                    }
                };
                document.body.appendChild(testBtn);
            }
        }, 1000);
    </script> -->
    
    <!-- Global JavaScript Configuration -->
    <script>
        // Global app configuration
        window.appConfig = {
            routes: {
                cartAdd: '<?php echo e(route("cart.add", ["locale" => app()->getLocale()])); ?>',
                cartCount: '<?php echo e(route("cart.count", ["locale" => app()->getLocale()])); ?>',
                cartIndex: '<?php echo e(route("cart.index", ["locale" => app()->getLocale()])); ?>',
                cartUpdate: '<?php echo e(url(app()->getLocale() . "/cart/update")); ?>',
                cartRemove: '<?php echo e(url(app()->getLocale() . "/cart/remove")); ?>',
                cartClear: '<?php echo e(route("cart.clear", ["locale" => app()->getLocale()])); ?>',
                cartCoupon: '<?php echo e(route("cart.coupon", ["locale" => app()->getLocale()])); ?>',
                cartCouponRemove: '<?php echo e(route("cart.coupon.remove", ["locale" => app()->getLocale()])); ?>',
                checkoutIndex: '<?php echo e(route("checkout.index", ["locale" => app()->getLocale()])); ?>',
                checkoutProcess: '<?php echo e(route("checkout.process", ["locale" => app()->getLocale()])); ?>',
            },
            locale: '<?php echo e(app()->getLocale()); ?>',
            csrfToken: '<?php echo e(csrf_token()); ?>'
        };

        // Language switcher
        function changeLanguage(locale) {
            const currentPath = window.location.pathname;
            const supportedLocales = ['en', 'fr', 'es'];

            // Check if current path starts with a locale
            const pathSegments = currentPath.split('/').filter(segment => segment !== '');
            let newPath = '';

            if (pathSegments.length > 0 && supportedLocales.includes(pathSegments[0])) {
                // Replace existing locale
                pathSegments[0] = locale;
                newPath = '/' + pathSegments.join('/');
            } else {
                // Add locale to path
                newPath = '/' + locale + currentPath;
            }

            // Preserve query parameters and hash
            const search = window.location.search;
            const hash = window.location.hash;

            window.location.href = newPath + search + hash;
        }
        
        // Currency switcher
        function changeCurrency(currency) {
            // Store in localStorage and reload
            localStorage.setItem('preferred_currency', currency);
            window.location.reload();
        }
        
        // Toast notification system
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';
            
            toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;
            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.getElementById('toast-container').appendChild(toast);
            
            // Animate in
            setTimeout(() => toast.classList.remove('translate-x-full'), 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }
        
        // Loading overlay functions
        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
        }
        
        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
        }
        
        // CSRF token for AJAX requests
        window.Laravel = {
            csrfToken: '<?php echo e(csrf_token()); ?>'
        };
        
        // Set up AJAX defaults
        if (typeof axios !== 'undefined') {
            axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
            axios.defaults.headers.common['X-CSRF-TOKEN'] = window.Laravel.csrfToken;
        }
    </script>

    <!-- Cart State Management -->
    <script src="<?php echo e(asset('js/cart-state-manager.js')); ?>"></script>
    <script src="<?php echo e(asset('js/cart-utils.js')); ?>"></script>

    <!-- Additional Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Footer Newsletter AJAX -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const footerNewsletterForm = document.getElementById('footer-newsletter-form');
        const footerNewsletterMessage = document.getElementById('footer-newsletter-message');
        const footerNewsletterMessageContent = document.getElementById('footer-newsletter-message-content');

        if (footerNewsletterForm) {
            footerNewsletterForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = this.querySelector('input[name="email"]').value;
                const consent = this.querySelector('input[name="consent"]').checked;
                const submitBtn = document.getElementById('footer-newsletter-submit');
                const originalText = submitBtn.textContent;

                // Basic validation
                if (!email || !consent) {
                    showFooterNewsletterMessage('Please fill in your email and agree to receive marketing emails.', 'error');
                    return;
                }

                // Show loading state
                submitBtn.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Subscribing...
                `;
                submitBtn.disabled = true;

                // Hide previous messages
                footerNewsletterMessage.classList.add('hidden');

                // Submit form via AJAX
                fetch(this.action, {
                    method: 'POST',
                    body: new FormData(this),
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showFooterNewsletterMessage(data.message, 'success');
                        // Clear form on success
                        this.reset();
                    } else {
                        let errorMessage = data.message || 'An error occurred. Please try again.';

                        // Show field errors if any
                        if (data.errors) {
                            const errorList = Object.values(data.errors).flat();
                            if (errorList.length > 0) {
                                errorMessage += ' ' + errorList.join(' ');
                            }
                        }

                        showFooterNewsletterMessage(errorMessage, data.type === 'info' ? 'info' : 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showFooterNewsletterMessage('An unexpected error occurred. Please try again later.', 'error');
                })
                .finally(() => {
                    // Restore button
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            });
        }

        function showFooterNewsletterMessage(message, type) {
            footerNewsletterMessage.classList.remove('hidden');

            // Remove existing classes
            footerNewsletterMessageContent.className = 'p-3 rounded-lg text-sm';

            // Add appropriate classes based on type
            if (type === 'success') {
                footerNewsletterMessageContent.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            } else if (type === 'info') {
                footerNewsletterMessageContent.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
            } else {
                footerNewsletterMessageContent.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
            }

            footerNewsletterMessageContent.textContent = message;

            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    footerNewsletterMessage.classList.add('hidden');
                }, 5000);
            }
        }
    });
    </script>

    <!-- Analytics -->
    <?php if(config('app.env') === 'production'): ?>
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo e(config('services.google.analytics_id')); ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?php echo e(config('services.google.analytics_id')); ?>');
        </script>
    <?php endif; ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\devs\chisolution\resources\views/layouts/app.blade.php ENDPATH**/ ?>