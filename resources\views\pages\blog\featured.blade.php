@extends('layouts.app')

@section('title', 'Featured Articles - Blog')
@section('meta_description', 'Discover our featured articles with expert insights, tutorials, and industry trends in web development, mobile apps, and digital marketing.')
@section('meta_keywords', 'featured articles, blog, web development, mobile apps, digital marketing, tutorials, insights')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></g></svg>');"></div>
    </div>
    
    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <!-- Breadcrumb -->
            <nav class="mb-8">
                <ol class="flex items-center justify-center space-x-2 text-sm text-blue-200">
                    <li><a href="{{ route('home', ['locale' => app()->getLocale()]) }}" class="hover:text-white transition-colors">Home</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li><a href="{{ route('blog.index') }}" class="hover:text-white transition-colors">Blog</a></li>
                    <li><span class="mx-2">/</span></li>
                    <li class="text-white">Featured</li>
                </ol>
            </nav>

            <h1 class="heading-1 text-white mb-6">
                Featured <span class="text-blue-300">Articles</span>
            </h1>
            <p class="text-lead text-blue-100 mb-8">
                Our handpicked selection of the most valuable articles, featuring expert insights, comprehensive tutorials, and cutting-edge industry trends.
            </p>
            <div class="text-blue-200">
                {{ $posts->total() }} featured {{ Str::plural('article', $posts->total()) }}
            </div>
        </div>
    </div>
    
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Featured Posts Grid -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                    <article class="blog-card card-hover bg-white rounded-lg shadow-lg overflow-hidden relative">
                        <!-- Featured Badge -->
                        <div class="absolute top-4 left-4 z-10">
                            <span class="px-3 py-1 bg-yellow-500 text-white text-sm font-semibold rounded-full shadow-lg">
                                ⭐ Featured
                            </span>
                        </div>
                        
                        <div class="relative overflow-hidden">
                            @if($post->featured_image)
                                <picture>
                                    @if($post->getWebPImageUrl($post->featured_image))
                                        <source srcset="{{ $post->getWebPImageUrl($post->featured_image) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->featured_image, 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @elseif($post->first_gallery_image_url)
                                <picture>
                                    @if($post->getWebPImageUrl($post->gallery_images[0]))
                                        <source srcset="{{ $post->getWebPImageUrl($post->gallery_images[0]) }}" type="image/webp">
                                    @endif
                                    <img src="{{ $post->getOptimizedImageUrl($post->gallery_images[0], 'medium') }}" 
                                         alt="{{ $post->title }}" 
                                         class="w-full h-48 object-cover hover-lift">
                                </picture>
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-yellow-500 to-yellow-600 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            @if($post->total_image_count > 1)
                                <div class="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ $post->total_image_count }}
                                </div>
                            @endif
                        </div>
                        
                        <div class="p-6 space-y-3">
                            <div class="flex items-center space-x-2">
                                @if($post->category)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">{{ $post->category->name }}</span>
                                @endif
                                <span class="text-gray-500 text-xs">{{ $post->formatted_published_date }}</span>
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ $post->excerpt }}
                            </p>
                            
                            @if($post->services()->count() > 0)
                                <div class="flex flex-wrap gap-1">
                                    @foreach($post->services()->take(2) as $service)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">{{ $service->name }}</span>
                                    @endforeach
                                    @if($post->services()->count() > 2)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">+{{ $post->services()->count() - 2 }} more</span>
                                    @endif
                                </div>
                            @endif
                            
                            <div class="flex items-center justify-between pt-3">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-xs">{{ substr($post->author->first_name, 0, 1) }}{{ substr($post->author->last_name, 0, 1) }}</span>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ $post->author->first_name }} {{ $post->author->last_name }}</span>
                                </div>
                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>{{ $post->reading_time }} min read</span>
                                    @if($post->view_count > 0)
                                        <span>•</span>
                                        <span>{{ number_format($post->view_count) }} views</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($posts->hasPages())
                <div class="flex justify-center mt-12">
                    {{ $posts->links('pagination::tailwind') }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Featured Articles Yet</h3>
                <p class="text-gray-600 mb-6">We're working on curating our best content. Check back soon for featured articles!</p>
                <a href="{{ route('blog.index') }}" class="btn-primary">
                    Browse All Articles
                </a>
            </div>
        @endif
    </div>
</section>

<!-- Why These Articles Are Featured -->
@if($posts->count() > 0)
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="heading-2 mb-6">
                    Why These Articles Are <span class="text-blue-600">Featured</span>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Expert Insights</h3>
                        <p class="text-gray-600 text-sm">Handpicked articles with deep technical knowledge and industry expertise.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">High Impact</h3>
                        <p class="text-gray-600 text-sm">Articles that provide maximum value and actionable insights for your projects.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Quality Assured</h3>
                        <p class="text-gray-600 text-sm">Thoroughly reviewed content that meets our highest standards for accuracy and usefulness.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endif

@push('styles')
<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
@endpush
@endsection
