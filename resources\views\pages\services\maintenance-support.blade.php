@extends('layouts.app')

@section('title', __('common.maintenance_support') . ' - ' . __('common.company_name'))
@section('meta_description', 'Leading website maintenance company in South Africa offering website security monitoring, performance optimization services, automated backups, 24/7 website monitoring, and comprehensive website support services.')
@section('meta_keywords', 'website maintenance services South Africa, website security monitoring, performance optimization services, automated backups, 24/7 website monitoring, website support services, security updates, website maintenance company, website uptime monitoring, website performance optimization')

@push('structured_data')
<script type="application/ld+json">
{
  "@type": "Service",
  "name": "Website Maintenance Services",
  "description": "Leading website maintenance company offering website security monitoring, performance optimization services, automated backups, and 24/7 website monitoring",
  "provider": {
    "@type": "Organization",
    "name": "{{ __('common.company_name') }}",
    "url": "{{ url('/') }}",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ZA",
      "addressRegion": "South Africa"
    }
  },
  "areaServed": {
    "@type": "Country",
    "name": "South Africa"
  },
  "serviceType": "Website Maintenance",
  "hasOfferCatalog": {
    "@type": "OfferCatalog",
    "name": "Website Maintenance Services",
    "itemListElement": [
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Website Security Monitoring",
          "description": "24/7 website security monitoring and threat detection services"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Performance Optimization Services",
          "description": "Website speed optimization and performance enhancement services"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "Automated Backups",
          "description": "Automated daily backups and disaster recovery solutions"
        }
      },
      {
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": "24/7 Website Monitoring",
          "description": "Round-the-clock website uptime monitoring and alert systems"
        }
      }
    ]
  },
  "keywords": "website maintenance services South Africa, website security monitoring, performance optimization services, automated backups, 24/7 website monitoring"
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-blue text-white overflow-hidden">
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><g fill=&quotnone&quot fill-rule=&quotevenodd&quot><g fill=&quot%23ffffff&quot fill-opacity=&quot0.1&quot><circle cx=&quot30&quot cy=&quot30&quot r=&quot2&quot/></g></g></svg>');"></div>
    </div>

    <div class="container mx-auto px-4 py-20 lg:py-32 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div class="text-center lg:text-left">
                <h1 class="heading-1 text-white mb-6">
                    {{ __('maintenance-support.hero_title') }}
                </h1>
                <p class="text-lead text-blue-100 mb-8">
                    {{ __('maintenance-support.hero_description') }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                        {{ __('maintenance-support.get_support') }}
                        <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </a>
                    <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="btn-outline bg-white border-white border-2 text-blue-600 font-semibold hover:bg-gray-50 hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                        {{ __('maintenance-support.view_work') }}
                    </a>
                </div>
            </div>

            <div class="relative">
                <div class="relative z-10">
                    <div class="bg-white rounded-xl shadow-xl p-8">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="group bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 border border-blue-200">
                                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-semibold text-gray-800">{{ __('maintenance-support.security') }}</div>
                            </div>
                            <div class="group bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 border border-green-200">
                                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-semibold text-gray-800">{{ __('maintenance-support.monitoring') }}</div>
                            </div>
                            <div class="group bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 text-center hover:shadow-lg transition-all duration-300 border border-purple-200">
                                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-semibold text-gray-800">{{ __('maintenance-support.backups') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Floating Elements -->
                <div class="absolute top-10 right-10 w-16 h-16 bg-blue-400 rounded-full opacity-20 animate-pulse"></div>
                <div class="absolute bottom-10 left-10 w-12 h-12 bg-blue-300 rounded-full opacity-30 animate-bounce"></div>
            </div>
        </div>
    </div>

    <!-- Wave Separator -->
    <div class="absolute bottom-0 left-0 w-full">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" class="relative block w-full h-16 fill-white">
            <path d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
        </svg>
    </div>
</section>

<!-- Services Section -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="heading-2 mb-4">
                {!! __('maintenance-support.main_title') !!}
            </h2>
            <p class="text-lead max-w-3xl mx-auto">
                {!! __('maintenance-support.main_description') !!}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Security Updates -->
            <div class="card-hover">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{!! __('maintenance-support.security_monitoring_title') !!}</h3>
                <p class="text-gray-600 mb-4">
                    {!! __('maintenance-support.security_monitoring_description') !!}
                </p>
                <ul class="text-sm text-gray-500 space-y-2">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.security_patches') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.malware_scanning') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.ssl_management') !!}
                    </li>
                </ul>
            </div>

            <!-- Performance Optimization -->
            <div class="card-hover">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{!! __('maintenance-support.performance_title') !!}</h3>
                <p class="text-gray-600 mb-4">
                    {!! __('maintenance-support.performance_description') !!}
                </p>
                <ul class="text-sm text-gray-500 space-y-2">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.speed_optimization') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.image_optimization') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.database_optimization') !!}
                    </li>
                </ul>
            </div>

            <!-- Backup & Recovery -->
            <div class="card-hover">
                <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-4">{!! __('maintenance-support.backup_title') !!}</h3>
                <p class="text-gray-600 mb-4">
                    {!! __('maintenance-support.backup_description') !!}
                </p>
                <ul class="text-sm text-gray-500 space-y-2">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.daily_backups') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.offsite_storage') !!}
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {!! __('maintenance-support.quick_recovery') !!}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-blue-600 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="heading-2 text-white mb-4">
            {!! __('maintenance-support.cta_title') !!}
        </h2>
        <p class="text-lead text-blue-100 mb-8 max-w-2xl mx-auto">
            {!! __('maintenance-support.cta_description') !!}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}" class="btn-primary bg-white text-blue-600 hover:bg-blue-50">
                {!! __('maintenance-support.get_maintenance_plan') !!}
                <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </a>
                <a href="{{ route('projects.index', ['locale' => app()->getLocale()]) }}" class="btn-outline bg-white border-white border-2 text-blue-600 font-semibold hover:bg-gray-50 hover:text-blue-600 hover:shadow-lg transition-all duration-300">
                    {!! __('maintenance-support.learn_more') !!}
                </a>
        </div>
    </div>
</section>
@endsection