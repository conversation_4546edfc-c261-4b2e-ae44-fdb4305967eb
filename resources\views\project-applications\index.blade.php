@extends('layouts.app')

@section('title', 'My Project Applications - ' . __('common.company_name'))

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Project Applications</h1>
                <p class="text-gray-600 mt-2">Track the status of your project applications</p>
            </div>
            <a href="{{ route('apply.project') }}" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                New Application
            </a>
        </div>

        @if(session('success'))
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                {{ session('success') }}
            </div>
        @endif

        @if($applications->count() > 0)
            <!-- Applications Grid -->
            <div class="grid gap-6">
                @foreach($applications as $application)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
                        <div class="p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h3 class="text-xl font-semibold text-gray-900">
                                            {{ $application->title }}
                                        </h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($application->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($application->status === 'approved') bg-green-100 text-green-800
                                            @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                            @else bg-blue-100 text-blue-800
                                            @endif">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @if($application->priority === 'low') bg-gray-100 text-gray-800
                                            @elseif($application->priority === 'medium') bg-blue-100 text-blue-800
                                            @elseif($application->priority === 'high') bg-orange-100 text-orange-800
                                            @else bg-red-100 text-red-800
                                            @endif">
                                            {{ ucfirst($application->priority) }} Priority
                                        </span>
                                    </div>
                                    
                                    @if($application->service)
                                        <p class="text-sm text-blue-600 mb-2">{{ $application->service->name }}</p>
                                    @endif
                                    
                                    <p class="text-gray-600 mb-4 line-clamp-2">
                                        {{ Str::limit($application->description, 200) }}
                                    </p>
                                    
                                    <div class="flex items-center space-x-6 text-sm text-gray-500">
                                        @if($application->budget_range)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                {{ ucfirst(str_replace('-', ' ', $application->budget_range)) }}
                                            </div>
                                        @endif
                                        
                                        @if($application->timeline)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ ucfirst(str_replace('-', ' ', $application->timeline)) }}
                                            </div>
                                        @endif
                                        
                                        @if($application->attachments && count($application->attachments) > 0)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                                </svg>
                                                {{ count($application->attachments) }} file(s)
                                            </div>
                                        @endif
                                        
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0a1 1 0 00-1 1v10a1 1 0 001 1h6a1 1 0 001-1V8a1 1 0 00-1-1"></path>
                                            </svg>
                                            {{ $application->created_at->format('M j, Y') }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col space-y-2 ml-4">
                                    <a href="{{ route('project-applications.show', $application) }}" 
                                       class="btn-outline border-blue-300 text-blue-700 hover:bg-blue-50 text-sm">
                                        View Details
                                    </a>
                                    
                                    @if($application->status === 'pending')
                                        <a href="{{ route('project-applications.edit', $application) }}" 
                                           class="btn-outline border-gray-300 text-gray-700 hover:bg-gray-50 text-sm">
                                            Edit
                                        </a>
                                    @endif
                                </div>
                            </div>
                            
                            @if($application->admin_notes && $application->status !== 'pending')
                                <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-900 mb-1">Admin Notes:</h4>
                                    <p class="text-sm text-gray-600">{{ $application->admin_notes }}</p>
                                    @if($application->reviewed_at)
                                        <p class="text-xs text-gray-500 mt-1">
                                            Reviewed on {{ $application->reviewed_at->format('M j, Y \a\t g:i A') }}
                                            @if($application->reviewedBy)
                                                by {{ $application->reviewedBy->full_name }}
                                            @endif
                                        </p>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($applications->hasPages())
                <div class="mt-8">
                    {{ $applications->links() }}
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No project applications</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by submitting your first project application.</p>
                <div class="mt-6">
                    <a href="{{ route('project-applications.create') }}" class="btn-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Submit Application
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
