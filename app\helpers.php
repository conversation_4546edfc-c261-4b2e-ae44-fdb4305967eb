<?php

if (!function_exists('image_service')) {
    /**
     * Get the ImageService instance
     */
    function image_service(): \App\Services\ImageService
    {
        return app(\App\Services\ImageService::class);
    }
}

if (!function_exists('process_image')) {
    /**
     * Quick helper to process an uploaded image
     */
    function process_image(\Illuminate\Http\UploadedFile $file, array $options = []): array
    {
        return image_service()->processUploadedImage($file, $options);
    }
}

if (!function_exists('quick_image_upload')) {
    /**
     * Quick image upload helper
     */
    function quick_image_upload(\Illuminate\Http\UploadedFile $file, string $subdirectory = 'uploads'): array
    {
        return image_service()->quickUpload($file, $subdirectory);
    }
}

if (!function_exists('full_image_upload')) {
    /**
     * Full image upload helper with variants
     */
    function full_image_upload(\Illuminate\Http\UploadedFile $file, string $subdirectory = 'uploads'): array
    {
        return image_service()->fullUpload($file, $subdirectory);
    }
}

if (!function_exists('optimize_image')) {
    /**
     * Optimize an existing image
     */
    function optimize_image(string $imagePath, array $options = []): bool
    {
        return image_service()->optimizeImage($imagePath, $options);
    }
}

if (!function_exists('resize_image')) {
    /**
     * Resize an image
     */
    function resize_image(string $imagePath, int $width, int $height, array $options = []): bool
    {
        return image_service()->resizeImage($imagePath, $width, $height, $options);
    }
}

if (!function_exists('convert_to_webp')) {
    /**
     * Convert image to WebP format
     */
    function convert_to_webp(string $imagePath, int $quality = null): ?string
    {
        return image_service()->convertToWebP($imagePath, $quality);
    }
}

if (!function_exists('validate_image_file')) {
    /**
     * Validate an uploaded image file
     */
    function validate_image_file(\Illuminate\Http\UploadedFile $file): array
    {
        return image_service()->validateImageFile($file);
    }
}

if (!function_exists('scan_image_for_viruses')) {
    /**
     * Scan image for viruses
     */
    function scan_image_for_viruses(string $filePath): array
    {
        return image_service()->scanForViruses($filePath);
    }
}

if (!function_exists('sanitize_image_filename')) {
    /**
     * Sanitize image filename
     */
    function sanitize_image_filename(string $filename): string
    {
        return image_service()->sanitizeFilename($filename);
    }
}

if (!function_exists('get_image_url')) {
    /**
     * Get public URL for image
     */
    function get_image_url(string $path): string
    {
        return image_service()->getImageUrl($path);
    }
}

if (!function_exists('delete_image')) {
    /**
     * Delete image and its variants
     */
    function delete_image(string $path, bool $deleteVariants = true): bool
    {
        return image_service()->deleteImage($path, $deleteVariants);
    }
}

if (!function_exists('get_image_info')) {
    /**
     * Get image information
     */
    function get_image_info(string $path): array
    {
        return image_service()->getImageInfo($path);
    }
}

// ============================================================================
// FILE SERVICE HELPERS
// ============================================================================

if (!function_exists('file_service')) {
    /**
     * Get FileService instance
     */
    function file_service(): \App\Services\FileService
    {
        return app(\App\Services\FileService::class);
    }
}

if (!function_exists('process_file')) {
    /**
     * Process uploaded file
     */
    function process_file(\Illuminate\Http\UploadedFile $file, array $options = []): array
    {
        return file_service()->processUploadedFile($file, $options);
    }
}

if (!function_exists('quick_file_upload')) {
    /**
     * Quick file upload
     */
    function quick_file_upload(\Illuminate\Http\UploadedFile $file, string $subdirectory = ''): array
    {
        return file_service()->quickUpload($file, $subdirectory);
    }
}

if (!function_exists('secure_file_upload')) {
    /**
     * Secure file upload with full processing
     */
    function secure_file_upload(\Illuminate\Http\UploadedFile $file, string $subdirectory = ''): array
    {
        return file_service()->secureUpload($file, $subdirectory);
    }
}

if (!function_exists('validate_file')) {
    /**
     * Validate uploaded file
     */
    function validate_file(\Illuminate\Http\UploadedFile $file): array
    {
        return file_service()->validateFile($file);
    }
}

if (!function_exists('scan_file_for_viruses')) {
    /**
     * Scan file for viruses
     */
    function scan_file_for_viruses(string $filePath): array
    {
        return file_service()->scanForViruses($filePath);
    }
}

if (!function_exists('sanitize_filename')) {
    /**
     * Sanitize filename
     */
    function sanitize_filename(string $filename): string
    {
        return file_service()->sanitizeFilename($filename);
    }
}

if (!function_exists('extract_file_text')) {
    /**
     * Extract text content from file
     */
    function extract_file_text(string $filePath): array
    {
        return file_service()->extractTextContent($filePath);
    }
}

if (!function_exists('process_archive_file')) {
    /**
     * Process archive file
     */
    function process_archive_file(string $filePath): array
    {
        return file_service()->processArchive($filePath);
    }
}

if (!function_exists('get_file_url')) {
    /**
     * Get file URL
     */
    function get_file_url(string $filePath): string
    {
        return file_service()->getFileUrl($filePath);
    }
}

if (!function_exists('delete_file')) {
    /**
     * Delete file
     */
    function delete_file(string $filePath): bool
    {
        return file_service()->deleteFile($filePath);
    }
}

if (!function_exists('get_file_info')) {
    /**
     * Get file information
     */
    function get_file_info(string $filePath): array
    {
        return file_service()->getFileInfo($filePath);
    }
}

if (!function_exists('remove_file_metadata')) {
    /**
     * Remove metadata from file
     */
    function remove_file_metadata(string $filePath): bool
    {
        return file_service()->removeMetadata($filePath);
    }
}

// ============================================================================
// PERFORMANCE AND SAFETY HELPERS
// ============================================================================

if (!function_exists('safe_htmlspecialchars')) {
    /**
     * Safe htmlspecialchars that handles arrays and objects
     */
    function safe_htmlspecialchars($value, int $flags = ENT_QUOTES, string $encoding = 'UTF-8', bool $double_encode = true): string
    {
        if (is_array($value) || is_object($value)) {
            return htmlspecialchars(json_encode($value), $flags, $encoding, $double_encode);
        }

        if (is_null($value)) {
            return '';
        }

        return htmlspecialchars((string) $value, $flags, $encoding, $double_encode);
    }
}

if (!function_exists('safe_string_cast')) {
    /**
     * Safely cast any value to string
     */
    function safe_string_cast($value): string
    {
        if (is_null($value)) {
            return '';
        }

        if (is_bool($value)) {
            return $value ? '1' : '0';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value) ?: '';
        }

        return (string) $value;
    }
}

if (!function_exists('performance_optimizer')) {
    /**
     * Get PerformanceOptimizer instance
     */
    function performance_optimizer(): \App\Services\PerformanceOptimizer
    {
        return app(\App\Services\PerformanceOptimizer::class);
    }
}

if (!function_exists('localized_route')) {
    /**
     * Generate a localized route URL
     */
    function localized_route(string $name, array $parameters = [], string $locale = null): string
    {
        $locale = $locale ?: app()->getLocale();

        // Add locale to parameters if not already present
        if (!isset($parameters['locale'])) {
            $parameters['locale'] = $locale;
        }

        return route($name, $parameters);
    }
}
